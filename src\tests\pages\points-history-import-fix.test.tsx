/**
 * PointsHistoryPage 导入修复验证测试
 * 专门验证 "Element type is invalid" 错误是否已修复
 */

import React from "react";
import { describe, it, expect, vi } from "vitest";
import { render } from "@testing-library/react";
import PointsHistoryPage from "@/app/profile/points/page";

// Mock next/navigation
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(() => ({
    back: vi.fn(),
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  })),
}));

// Mock AuthGuard - 使用默认导入
vi.mock("@/components/AuthGuard", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-guard">{children}</div>
  ),
}));

// Mock PageContainer
vi.mock("@/components/layout/PageContainer", () => ({
  PageContainer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="page-container">{children}</div>
  ),
}));

// Mock PointsHistory
vi.mock("@/components/profile/PointsHistory", () => ({
  PointsHistory: () => <div data-testid="points-history">积分历史组件</div>,
}));

// Mock lucide-react icons
vi.mock("lucide-react", () => ({
  ArrowLeft: () => <span data-testid="arrow-left-icon">←</span>,
  Award: () => <span data-testid="award-icon">🏆</span>,
  TrendingUp: () => <span data-testid="trending-up-icon">📈</span>,
  Info: () => <span data-testid="info-icon">ℹ️</span>,
}));

describe("PointsHistoryPage 导入修复验证", () => {
  it("应该能够正确渲染而不抛出 Element type is invalid 错误", () => {
    // 这个测试的主要目的是验证组件能够正确导入和渲染
    // 如果 AuthGuard 导入有问题，这里会抛出 "Element type is invalid" 错误
    expect(() => {
      render(<PointsHistoryPage />);
    }).not.toThrow();
  });

  it("应该正确渲染 AuthGuard 组件", () => {
    const { getByTestId } = render(<PointsHistoryPage />);
    
    // 验证 AuthGuard 组件能够正确渲染
    expect(getByTestId("auth-guard")).toBeDefined();
  });

  it("应该正确渲染页面基本结构", () => {
    const { getByTestId, getByText } = render(<PointsHistoryPage />);
    
    // 验证基本组件都能正确渲染
    expect(getByTestId("auth-guard")).toBeDefined();
    expect(getByTestId("page-container")).toBeDefined();
    expect(getByTestId("points-history")).toBeDefined();
    
    // 验证页面标题
    expect(getByText("积分历史")).toBeDefined();
    expect(getByText("返回")).toBeDefined();
  });

  it("验证所有导入的组件都能正确工作", () => {
    const { getByTestId } = render(<PointsHistoryPage />);
    
    // 验证所有 mock 的组件都能正确渲染
    expect(getByTestId("auth-guard")).toBeDefined();
    expect(getByTestId("page-container")).toBeDefined();
    expect(getByTestId("points-history")).toBeDefined();
    expect(getByTestId("arrow-left-icon")).toBeDefined();
    expect(getByTestId("award-icon")).toBeDefined();
    expect(getByTestId("trending-up-icon")).toBeDefined();
    expect(getByTestId("info-icon")).toBeDefined();
  });
});
