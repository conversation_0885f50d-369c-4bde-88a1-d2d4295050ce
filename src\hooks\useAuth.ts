"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
  getCurrentUser,
  isAuthenticated,
  logout as authLogout,
  User,
} from "@/services/authService";
import { useToast } from "@/components/ToastProvider";

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export function useAuth() {
  const router = useRouter();
  const { showToast } = useToast();

  // 修复水合错误：确保服务端和客户端的初始状态一致
  // 在初始化时，无论是否有token，都设置为未认证状态
  // 然后通过useEffect在客户端挂载后检查真实的认证状态
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false, // 始终从false开始，避免水合不匹配
  });

  // 添加一个标记来跟踪是否已经挂载
  const [isMounted, setIsMounted] = useState(false);

  // 检查认证状态
  const checkAuth = useCallback(async () => {
    try {
      // 先检查客户端环境
      if (typeof window === "undefined") {
        setAuthState((prev) => ({ ...prev, isLoading: false }));
        return;
      }

      const isAuth = isAuthenticated();

      if (!isAuth) {
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
        });
        return;
      }

      // 获取用户信息
      const user = await getCurrentUser();

      if (user) {
        setAuthState({
          user,
          isLoading: false,
          isAuthenticated: true,
        });
      } else {
        // 如果获取用户信息失败，检查是否仍有有效token
        const token = localStorage.getItem("auth_token");
        if (token) {
          console.warn(
            "🔐 checkAuth: 有token但获取用户信息失败，可能是后端兼容性问题"
          );
          // 保持认证状态，但用户信息为空
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: true, // 保持认证状态
          });
        } else {
          // 没有token，清除认证状态
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
          });
        }
      }
    } catch (error) {
      console.error("🔐 checkAuth: 认证检查失败:", error);
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
      });
    }
  }, []);

  // 登出
  const logout = useCallback(async () => {
    try {
      const result = await authLogout();
      if (result.success) {
        showToast(result.message, "success");
      }
    } catch (error) {
      console.error("登出失败:", error);
    } finally {
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
      });
      router.push("/login");
    }
  }, [router, showToast]);

  // 刷新用户信息
  const refreshUser = useCallback(async () => {
    if (!isAuthenticated()) return;

    try {
      const user = await getCurrentUser();
      if (user) {
        setAuthState((prev) => ({
          ...prev,
          user,
        }));
      }
    } catch (error) {
      console.error("刷新用户信息失败:", error);
    }
  }, []);

  // 设置用户信息
  const setUser = useCallback((user: User | null) => {
    setAuthState((prev) => ({
      ...prev,
      user,
    }));
  }, []);

  // 检查是否有特定权限
  const hasPermission = useCallback(
    (requiredRole: string) => {
      if (!authState.user || !authState.user.role) {
        return false;
      }

      const roleHierarchy: { [key: string]: number } = {
        user: 1,
        moderator: 2,
        admin: 3,
      };

      const userLevel = roleHierarchy[authState.user.role.name] || 0;
      const requiredLevel = roleHierarchy[requiredRole] || 0;
      const hasAccess = userLevel >= requiredLevel;

      return hasAccess;
    },
    [authState.user]
  );

  // 检查是否为管理员
  const isAdmin = useCallback(() => {
    const result = authState.user?.role?.name === "admin";
    console.log("🔐 useAuth.isAdmin:", {
      userRole: authState.user?.role,
      isAdmin: result,
    });
    return result;
  }, [authState.user]);

  // 检查是否为版主或管理员
  const isModerator = useCallback(() => {
    const result =
      authState.user?.role?.name === "moderator" ||
      authState.user?.role?.name === "admin";
    console.log("🔐 useAuth.isModerator:", {
      userRole: authState.user?.role,
      isModerator: result,
    });
    return result;
  }, [authState.user]);

  // 要求登录
  const requireAuth = useCallback(
    (redirectTo?: string) => {
      if (!authState.isAuthenticated) {
        const currentPath = window.location.pathname + window.location.search;
        const redirect = redirectTo || currentPath;
        router.push(`/login?redirect=${encodeURIComponent(redirect)}`);
        return false;
      }
      return true;
    },
    [authState.isAuthenticated, router]
  );

  // 要求特定权限
  const requirePermission = useCallback(
    (requiredRole: string, redirectTo?: string) => {
      if (!requireAuth(redirectTo)) return false;

      if (!hasPermission(requiredRole)) {
        showToast("您没有权限访问此页面", "error");
        router.push("/");
        return false;
      }

      return true;
    },
    [requireAuth, hasPermission, showToast, router]
  );

  // 要求管理员权限
  const requireAdmin = useCallback(
    (redirectTo?: string) => {
      return requirePermission("admin", redirectTo);
    },
    [requirePermission]
  );

  // 组件挂载后设置挂载状态并检查认证
  useEffect(() => {
    setIsMounted(true);
    checkAuth();
  }, [checkAuth]); // 添加 checkAuth 依赖

  // 监听存储变化（多标签页同步）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "auth_token") {
        if (!e.newValue) {
          // Token被删除，用户在其他标签页登出
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
          });
        } else {
          // Token被更新，重新检查认证状态
          // 直接调用checkAuth，不依赖useCallback
          (async () => {
            try {
              const isAuth = isAuthenticated();
              if (!isAuth) {
                setAuthState({
                  user: null,
                  isLoading: false,
                  isAuthenticated: false,
                });
                return;
              }

              const user = await getCurrentUser();
              if (user) {
                setAuthState({
                  user,
                  isLoading: false,
                  isAuthenticated: true,
                });
              } else {
                setAuthState({
                  user: null,
                  isLoading: false,
                  isAuthenticated: false,
                });
              }
            } catch {
              setAuthState({
                user: null,
                isLoading: false,
                isAuthenticated: false,
              });
            }
          })();
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []); // 空依赖数组

  return {
    // 状态 - 在未挂载时确保返回安全的默认值
    user: authState.user,
    isLoading: authState.isLoading || !isMounted, // 未挂载时也显示为加载中
    isAuthenticated: isMounted ? authState.isAuthenticated : false, // 未挂载时始终为false

    // 方法
    logout,
    refreshUser,
    checkAuth,
    setUser,

    // 权限检查
    hasPermission,
    isAdmin,
    isModerator,

    // 权限要求
    requireAuth,
    requirePermission,
    requireAdmin,
  };
}
