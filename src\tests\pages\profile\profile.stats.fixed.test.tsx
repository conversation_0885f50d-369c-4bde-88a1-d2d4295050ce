import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { useRouter } from "next/navigation";
import ProfilePage from "@/app/profile/page";

// Mock dependencies
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
}));

vi.mock("@/services/profileService", () => ({
  getProfileStatistics: vi.fn(),
}));

vi.mock("@/services/authService", () => ({
  getCurrentUser: vi.fn(),
}));

vi.mock("@/components/AuthGuard", () => ({
  default: function MockAuthGuard({ children }: { children: React.ReactNode }) {
    return <div>{children}</div>;
  },
}));

describe("ProfilePage - 修复后的统计数据测试", () => {
  const mockPush = vi.fn();
  const mockGetProfileStatistics = vi.mocked(
    require("@/services/profileService").getProfileStatistics
  );
  const mockGetCurrentUser = vi.mocked(
    require("@/services/authService").getCurrentUser
  );

  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as any).mockReturnValue({
      push: mockPush,
    });
  });

  it("应该使用用户基本信息作为备用数据源", async () => {
    // Mock 统计API失败
    mockGetProfileStatistics.mockResolvedValue({
      success: false,
      message: "统计API失败",
    });

    // Mock 用户基本信息成功
    mockGetCurrentUser.mockResolvedValue({
      id: 28,
      username: "testwdk",
      email: "<EMAIL>",
      nickname: "吃土豆不吃土豆皮",
      points: 20,
      title: "资源拾荒者",
      role: { name: "user" },
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 检查是否显示正确的积分
      expect(screen.getByText("20")).toBeInTheDocument();
      // 检查是否显示正确的等级
      expect(screen.getByText("当前等级：资源拾荒者")).toBeInTheDocument();
    });
  });

  it("应该正确处理统计API返回空数据的情况", async () => {
    // Mock 统计API返回成功但数据为空
    mockGetProfileStatistics.mockResolvedValue({
      success: true,
      data: {
        total_points: undefined,
        current_title: undefined,
        total_help_requests: undefined,
        total_help_answers: undefined,
        accepted_answers: undefined,
      },
    });

    // Mock 用户基本信息
    mockGetCurrentUser.mockResolvedValue({
      id: 28,
      username: "testwdk",
      points: 20,
      title: "资源拾荒者",
      role: { name: "user" },
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 应该使用备用数据源
      expect(screen.getByText("20")).toBeInTheDocument();
      expect(screen.getByText("当前等级：资源拾荒者")).toBeInTheDocument();
    });
  });

  it("应该优先使用完整的统计数据", async () => {
    // Mock 统计API返回完整数据
    mockGetProfileStatistics.mockResolvedValue({
      success: true,
      data: {
        total_points: 150,
        current_title: "云盘勘探员",
        total_help_requests: 5,
        total_help_answers: 10,
        accepted_answers: 3,
      },
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 应该使用统计API的数据
      expect(screen.getByText("150")).toBeInTheDocument();
      expect(screen.getByText("当前等级：云盘勘探员")).toBeInTheDocument();
      expect(screen.getByText("5")).toBeInTheDocument(); // 发起求助
      expect(screen.getByText("10")).toBeInTheDocument(); // 提供回答
      expect(screen.getByText("3")).toBeInTheDocument(); // 被采纳回答
    });
  });

  it("应该处理所有API都失败的情况", async () => {
    // Mock 所有API都失败
    mockGetProfileStatistics.mockRejectedValue(new Error("网络错误"));
    mockGetCurrentUser.mockRejectedValue(new Error("认证失败"));

    render(<ProfilePage />);

    await waitFor(() => {
      // 页面应该正常渲染，但没有统计数据
      expect(screen.getByText("活动统计")).toBeInTheDocument();
    });
  });

  it("应该正确显示默认等级", async () => {
    // Mock 统计API失败
    mockGetProfileStatistics.mockResolvedValue({
      success: false,
      message: "API失败",
    });

    // Mock 用户信息但没有title
    mockGetCurrentUser.mockResolvedValue({
      id: 1,
      username: "newuser",
      points: 0,
      title: undefined, // 没有title
      role: { name: "user" },
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 应该显示默认等级
      expect(screen.getByText("当前等级：资源拾荒者")).toBeInTheDocument();
    });
  });
});
