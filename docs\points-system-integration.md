# 积分系统集成文档

## 概述

根据用户需求，在个人信息积分历史页面内实现了以下功能：

1. **积分历史记录**：展示用户的积分获得和消费记录（已过滤本月数据）
2. **积分获取说明**：通过后端接口获取积分规则说明
3. **积分排行榜**：展示前10名用户的积分排名
4. **去除返回按钮**：移除了页面的返回按钮

## 页面组件排序

按照用户要求，组件排序为：
1. 积分历史记录（主要内容区域）
2. 积分获取说明（左侧）
3. 积分排行榜（右侧，取前10名）

## 实现的功能

### 1. 积分历史记录过滤
- 修改了 `PointsHistory` 组件，过滤掉本月的积分数据
- 只显示历史积分记录，不包含当前月份的数据

### 2. 积分获取说明组件 (`PointsRules`)
- 新建组件：`src/components/profile/PointsRules.tsx`
- 调用后端接口：`/api/points/rules`
- 显示积分获得规则、消费规则和等级系统
- 支持后端数据和默认数据的展示

### 3. 积分排行榜组件 (`PointsLeaderboard`)
- 新建组件：`src/components/profile/PointsLeaderboard.tsx`
- 调用后端接口：`/api/points/leaderboard?limit=10`
- 显示前10名用户的积分排名
- 支持用户头像、昵称、积分和头衔的展示
- 前三名有特殊的排名图标显示

### 4. 服务接口扩展
在 `src/services/profileService.ts` 中新增：
- `getPointsRules()`: 获取积分规则说明
- `getPointsLeaderboard(limit)`: 获取积分排行榜

### 5. API路由创建
- `src/app/api/points/rules/route.ts`: 积分规则接口
- `src/app/api/points/leaderboard/route.ts`: 积分排行榜接口

### 6. 页面重构
- 修改 `src/app/profile/points/page.tsx`
- 移除返回按钮
- 重新组织页面布局，按照用户要求的组件顺序排列

## 接口对接

### 积分规则接口
```
GET /api/points/rules
```
- 无需认证
- 返回积分获得规则、消费规则和等级系统信息

### 积分排行榜接口
```
GET /api/points/leaderboard?limit=10
```
- 无需认证
- 返回前N名用户的积分排名信息

### 积分历史接口（已存在）
```
GET /api/profile/points-history?page=1&size=20
```
- 需要认证
- 返回用户的积分历史记录（已过滤本月数据）

## 测试文件

创建了对应的测试文件：
- `src/tests/components/profile/PointsRules.test.tsx`
- `src/tests/components/profile/PointsLeaderboard.test.tsx`

## 页面效果

1. **积分历史记录**：位于页面顶部，显示用户的历史积分变化
2. **积分获取说明**：位于下方左侧，显示积分规则和等级系统
3. **积分排行榜**：位于下方右侧，显示前10名用户排名

## 技术特点

- 响应式设计，支持移动端和桌面端
- 错误处理和加载状态
- 数据过滤（去除本月统计）
- 组件化设计，便于维护和复用
- 遵循项目的代码规范和最佳实践

## 注意事项

1. 积分历史记录已过滤本月数据，只显示历史记录
2. 积分排行榜默认显示前10名用户
3. 页面已移除返回按钮
4. 所有新组件都有对应的测试文件
5. 接口调用都有错误处理和默认数据支持
