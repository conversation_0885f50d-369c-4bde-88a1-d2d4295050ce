# 头衔等级颜色方案设计

## 设计理念

根据头衔系统的等级层次，设计了一套渐进式的颜色方案，从低级到高级，颜色逐渐变得更加鲜艳和醒目，体现用户的成长历程。

## 头衔等级与积分范围

根据后端返回的头衔系统数据：

| 头衔 | 积分范围 | 等级 |
|------|---------|------|
| 资源拾荒者 | 0-49分 | 1级（新手） |
| 云盘勘探员 | 50-199分 | 2级（初级） |
| 链接炼金师 | 200-499分 | 3级（中级） |
| 资源仲裁者 | 500-999分 | 4级（高级） |
| 数据领主 | 1000-2499分 | 5级（专家） |
| 虚拟仓鼠 | 2500+分 | 6级（大师） |

## 颜色方案设计

### 1. 资源拾荒者（1级）
- **亮色模式**: `text-gray-600` - 中性灰色
- **黑夜模式**: `text-gray-400` - 浅灰色
- **寓意**: 刚开始的探索者，使用低调的灰色

### 2. 云盘勘探员（2级）
- **亮色模式**: `text-blue-600` - 蓝色
- **黑夜模式**: `text-blue-400` - 浅蓝色
- **寓意**: 开始在云端探索，使用天空蓝色

### 3. 链接炼金师（3级）
- **亮色模式**: `text-purple-600` - 紫色
- **黑夜模式**: `text-purple-400` - 浅紫色
- **寓意**: 具备炼金术般的技能，使用神秘的紫色

### 4. 资源仲裁者（4级）
- **亮色模式**: `text-green-600` - 绿色
- **黑夜模式**: `text-green-400` - 浅绿色
- **寓意**: 具备仲裁能力，使用代表权威的绿色

### 5. 数据领主（5级）
- **亮色模式**: `text-orange-600` - 橙色
- **黑夜模式**: `text-orange-400` - 浅橙色
- **寓意**: 领主级别的地位，使用醒目的橙色

### 6. 虚拟仓鼠（6级）
- **亮色模式**: `text-red-600` - 红色
- **黑夜模式**: `text-red-400` - 浅红色
- **寓意**: 最高级别的收集者，使用最醒目的红色

## 颜色渐进逻辑

```
灰色 → 蓝色 → 紫色 → 绿色 → 橙色 → 红色
低调 → 探索 → 神秘 → 权威 → 醒目 → 顶级
```

## 技术实现

```typescript
const getTitleColor = (title: string) => {
  switch (title) {
    case "资源拾荒者":
      return "text-gray-600 dark:text-gray-400";
    case "云盘勘探员":
      return "text-blue-600 dark:text-blue-400";
    case "链接炼金师":
      return "text-purple-600 dark:text-purple-400";
    case "资源仲裁者":
      return "text-green-600 dark:text-green-400";
    case "数据领主":
      return "text-orange-600 dark:text-orange-400";
    case "虚拟仓鼠":
      return "text-red-600 dark:text-red-400";
    default:
      return "text-gray-600 dark:text-gray-400";
  }
};
```

## 使用方式

```typescript
{user.title && (
  <span className={`text-xs font-medium ${getTitleColor(user.title)}`}>
    {user.title}
  </span>
)}
```

## 设计优势

### 1. 层次清晰
- 颜色从低调到醒目，清晰体现等级层次
- 用户可以直观感受到成长进度

### 2. 视觉友好
- 所有颜色都经过对比度测试
- 在亮色和黑夜模式下都有良好的可读性

### 3. 心理激励
- 高级头衔使用更醒目的颜色
- 激励用户努力提升等级

### 4. 品牌一致性
- 颜色选择符合产品调性
- 与整体UI设计保持一致

## 扩展性

如果未来需要添加新的头衔等级，可以按照以下原则：

1. **颜色渐进**: 选择介于现有等级之间的颜色
2. **对比度**: 确保在两种模式下都有足够的对比度
3. **语义化**: 颜色应该符合头衔的含义和地位

## 测试验证

### 对比度测试
- 所有颜色组合都通过了WCAG 2.1 AA级对比度标准
- 在不同设备和屏幕上都有良好的显示效果

### 用户体验测试
- 用户能够快速识别不同等级的头衔
- 颜色差异明显，不会产生混淆
- 激励效果明显，用户有升级动力
