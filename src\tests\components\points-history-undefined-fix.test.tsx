/**
 * PointsHistory 组件 undefined 错误修复验证测试
 * 验证 "Cannot read properties of undefined (reading 'length')" 错误是否已修复
 */

import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import { PointsHistory } from "@/components/profile/PointsHistory";

// Mock lucide-react icons
vi.mock("lucide-react", () => ({
  TrendingUp: () => <span data-testid="trending-up">📈</span>,
  TrendingDown: () => <span data-testid="trending-down">📉</span>,
  Calendar: () => <span data-testid="calendar">📅</span>,
  AlertCircle: () => <span data-testid="alert-circle">⚠️</span>,
  RefreshCw: () => <span data-testid="refresh">🔄</span>,
}));

// Mock UI components
vi.mock("@/components/ui/card", () => ({
  Card: ({ children, className }: any) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
  CardContent: ({ children }: any) => (
    <div data-testid="card-content">{children}</div>
  ),
  CardHeader: ({ children }: any) => (
    <div data-testid="card-header">{children}</div>
  ),
  CardTitle: ({ children }: any) => (
    <div data-testid="card-title">{children}</div>
  ),
}));

vi.mock("@/components/ui/Button", () => ({
  Button: ({ children, onClick, disabled }: any) => (
    <button data-testid="button" onClick={onClick} disabled={disabled}>
      {children}
    </button>
  ),
}));

vi.mock("@/components/ui/badge", () => ({
  Badge: ({ children, className }: any) => (
    <span data-testid="badge" className={className}>
      {children}
    </span>
  ),
}));

vi.mock("@/components/ui/skeleton", () => ({
  Skeleton: ({ className }: any) => (
    <div data-testid="skeleton" className={className}></div>
  ),
}));

vi.mock("@/components/ui/alert", () => ({
  Alert: ({ children, variant }: any) => (
    <div data-testid="alert" data-variant={variant}>
      {children}
    </div>
  ),
  AlertDescription: ({ children }: any) => (
    <div data-testid="alert-description">{children}</div>
  ),
}));

vi.mock("@/components/ui/table", () => ({
  Table: ({ children }: any) => (
    <table data-testid="table">{children}</table>
  ),
  TableBody: ({ children }: any) => (
    <tbody data-testid="table-body">{children}</tbody>
  ),
  TableCell: ({ children }: any) => (
    <td data-testid="table-cell">{children}</td>
  ),
  TableHead: ({ children }: any) => (
    <th data-testid="table-head">{children}</th>
  ),
  TableHeader: ({ children }: any) => (
    <thead data-testid="table-header">{children}</thead>
  ),
  TableRow: ({ children }: any) => (
    <tr data-testid="table-row">{children}</tr>
  ),
}));

vi.mock("@/components/ui/pagination", () => ({
  Pagination: ({ children }: any) => (
    <div data-testid="pagination">{children}</div>
  ),
  PaginationContent: ({ children }: any) => (
    <div data-testid="pagination-content">{children}</div>
  ),
  PaginationEllipsis: () => <span data-testid="pagination-ellipsis">...</span>,
  PaginationItem: ({ children }: any) => (
    <div data-testid="pagination-item">{children}</div>
  ),
  PaginationLink: ({ children, onClick }: any) => (
    <button data-testid="pagination-link" onClick={onClick}>
      {children}
    </button>
  ),
  PaginationNext: ({ onClick }: any) => (
    <button data-testid="pagination-next" onClick={onClick}>
      下一页
    </button>
  ),
  PaginationPrevious: ({ onClick }: any) => (
    <button data-testid="pagination-previous" onClick={onClick}>
      上一页
    </button>
  ),
}));

describe("PointsHistory 组件 undefined 错误修复验证", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("当 getPointsHistory 返回 undefined items 时不应该崩溃", async () => {
    // Mock 返回 undefined items 的情况
    vi.doMock("@/services/profileService", () => ({
      getPointsHistory: vi.fn(() =>
        Promise.resolve({
          success: true,
          data: {
            items: undefined, // 模拟 undefined 情况
            total: 0,
            page: 1,
            size: 10,
            total_pages: 1,
          },
        })
      ),
    }));

    expect(() => {
      render(<PointsHistory />);
    }).not.toThrow();

    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.getByTestId("card")).toBeDefined();
    });
  });

  it("当 getPointsHistory 返回 null data 时不应该崩溃", async () => {
    // Mock 返回 null data 的情况
    vi.doMock("@/services/profileService", () => ({
      getPointsHistory: vi.fn(() =>
        Promise.resolve({
          success: true,
          data: null, // 模拟 null 情况
        })
      ),
    }));

    expect(() => {
      render(<PointsHistory />);
    }).not.toThrow();

    await waitFor(() => {
      expect(screen.getByTestId("card")).toBeDefined();
    });
  });

  it("当 getPointsHistory 返回错误时不应该崩溃", async () => {
    // Mock 返回错误的情况
    vi.doMock("@/services/profileService", () => ({
      getPointsHistory: vi.fn(() =>
        Promise.resolve({
          success: false,
          message: "获取失败",
        })
      ),
    }));

    expect(() => {
      render(<PointsHistory />);
    }).not.toThrow();

    await waitFor(() => {
      expect(screen.getByTestId("card")).toBeDefined();
    });
  });

  it("当 getPointsHistory 抛出异常时不应该崩溃", async () => {
    // Mock 抛出异常的情况
    vi.doMock("@/services/profileService", () => ({
      getPointsHistory: vi.fn(() => Promise.reject(new Error("网络错误"))),
    }));

    expect(() => {
      render(<PointsHistory />);
    }).not.toThrow();

    await waitFor(() => {
      expect(screen.getByTestId("card")).toBeDefined();
    });
  });

  it("当 getPointsHistory 返回正常数据时应该正确渲染", async () => {
    // Mock 返回正常数据的情况
    vi.doMock("@/services/profileService", () => ({
      getPointsHistory: vi.fn(() =>
        Promise.resolve({
          success: true,
          data: {
            items: [
              {
                id: 1,
                points: 10,
                transaction_type: "earn",
                description: "回答问题",
                created_at: "2023-01-01T00:00:00Z",
              },
            ],
            total: 1,
            page: 1,
            size: 10,
            total_pages: 1,
          },
        })
      ),
    }));

    expect(() => {
      render(<PointsHistory />);
    }).not.toThrow();

    await waitFor(() => {
      expect(screen.getByTestId("card")).toBeDefined();
    });
  });

  it("应该正确处理空数组情况", async () => {
    // Mock 返回空数组的情况
    vi.doMock("@/services/profileService", () => ({
      getPointsHistory: vi.fn(() =>
        Promise.resolve({
          success: true,
          data: {
            items: [],
            total: 0,
            page: 1,
            size: 10,
            total_pages: 1,
          },
        })
      ),
    }));

    expect(() => {
      render(<PointsHistory />);
    }).not.toThrow();

    await waitFor(() => {
      expect(screen.getByTestId("card")).toBeDefined();
    });
  });
});
