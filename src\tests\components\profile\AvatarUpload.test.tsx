import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { AvatarUpload } from "@/components/profile/AvatarUpload";
import * as profileService from "@/services/profileService";
import * as errorHandler from "@/utils/errorHandler";

// Mock dependencies
vi.mock("@/services/profileService");
vi.mock("@/utils/errorHandler");

// Mock lucide-react icons
vi.mock("lucide-react", () => ({
  Upload: () => <div data-testid="upload-icon" />,
  Camera: () => <div data-testid="camera-icon" />,
  AlertCircle: () => <div data-testid="alert-circle-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
}));

describe("AvatarUpload", () => {
  const defaultProps = {
    username: "testuser",
    nickname: "Test User",
    currentAvatar: "https://example.com/avatar.jpg",
    onUploadSuccess: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(errorHandler.validateAvatarFile).mockReturnValue({ valid: true });
  });

  it("应该正确渲染组件", () => {
    render(<AvatarUpload {...defaultProps} />);

    expect(screen.getByText("头像设置")).toBeInTheDocument();
    expect(screen.getByText("T")).toBeInTheDocument(); // 昵称首字母
  });

  it("当nickname为null时应该使用username的首字母", () => {
    render(<AvatarUpload {...defaultProps} nickname={null} />);

    expect(screen.getByText("T")).toBeInTheDocument(); // username首字母
  });

  it("当nickname和username都为空时应该显示默认字母", () => {
    render(<AvatarUpload {...defaultProps} nickname={null} username="" />);

    expect(screen.getByText("U")).toBeInTheDocument(); // 默认字母
  });

  it("当nickname和username都为undefined时应该显示默认字母", () => {
    render(
      <AvatarUpload
        {...defaultProps}
        nickname={undefined}
        username={undefined as any}
      />
    );

    expect(screen.getByText("U")).toBeInTheDocument(); // 默认字母
  });

  it("应该处理文件选择", async () => {
    vi.mocked(errorHandler.validateAvatarFile).mockReturnValue({ valid: true });

    render(<AvatarUpload {...defaultProps} />);

    const file = new File(["test"], "test.jpg", { type: "image/jpeg" });

    // 模拟文件选择
    const hiddenInput = document.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement;
    Object.defineProperty(hiddenInput, "files", {
      value: [file],
      writable: false,
    });

    fireEvent.change(hiddenInput);

    expect(vi.mocked(errorHandler.validateAvatarFile)).toHaveBeenCalledWith(
      file
    );
  });

  it("应该显示文件验证错误", () => {
    vi.mocked(errorHandler.validateAvatarFile).mockReturnValue({
      valid: false,
      error: "文件格式不支持",
    });

    render(<AvatarUpload {...defaultProps} />);

    const hiddenInput = document.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement;
    const file = new File(["test"], "test.txt", { type: "text/plain" });

    Object.defineProperty(hiddenInput, "files", {
      value: [file],
      writable: false,
    });

    fireEvent.change(hiddenInput);

    expect(screen.getByText("文件格式不支持")).toBeInTheDocument();
  });

  it("应该处理上传成功", async () => {
    const mockOnUploadSuccess = vi.fn();
    vi.mocked(profileService.uploadAvatar).mockResolvedValue({
      success: true,
      data: { avatarUrl: "https://example.com/new-avatar.jpg" },
    });

    render(
      <AvatarUpload {...defaultProps} onUploadSuccess={mockOnUploadSuccess} />
    );

    const uploadButton = screen.getByRole("button", { name: /上传头像/i });

    // 先选择文件
    const hiddenInput = document.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement;
    const file = new File(["test"], "test.jpg", { type: "image/jpeg" });

    Object.defineProperty(hiddenInput, "files", {
      value: [file],
      writable: false,
    });

    fireEvent.change(hiddenInput);

    // 然后点击上传
    fireEvent.click(uploadButton);

    await waitFor(() => {
      expect(vi.mocked(profileService.uploadAvatar)).toHaveBeenCalledWith(
        file,
        expect.any(Function)
      );
    });
  });

  it("应该处理上传失败", async () => {
    vi.mocked(profileService.uploadAvatar).mockRejectedValue(
      new Error("上传失败")
    );

    render(<AvatarUpload {...defaultProps} />);

    const hiddenInput = document.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement;
    const file = new File(["test"], "test.jpg", { type: "image/jpeg" });

    Object.defineProperty(hiddenInput, "files", {
      value: [file],
      writable: false,
    });

    fireEvent.change(hiddenInput);

    const uploadButton = screen.getByRole("button", { name: /上传头像/i });
    fireEvent.click(uploadButton);

    await waitFor(() => {
      expect(screen.getByText(/上传失败/)).toBeInTheDocument();
    });
  });

  it("应该正确显示当前头像", () => {
    render(<AvatarUpload {...defaultProps} />);

    const avatarImage = document.querySelector('img[alt="Test User"]');
    expect(avatarImage).toHaveAttribute(
      "src",
      "https://example.com/avatar.jpg"
    );
  });

  it("当没有当前头像时应该只显示fallback", () => {
    render(<AvatarUpload {...defaultProps} currentAvatar={null} />);

    expect(screen.getByText("T")).toBeInTheDocument();
  });
});
