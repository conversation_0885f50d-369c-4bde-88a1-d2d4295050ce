import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { vi } from "vitest";
import { PointsRules } from "@/components/profile/PointsRules";
import { getPointsRules } from "@/services/profileService";

// Mock the service
vi.mock("@/services/profileService", () => ({
  getPointsRules: vi.fn(),
}));

const mockGetPointsRules = vi.mocked(getPointsRules);

describe("PointsRules", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该显示加载状态", () => {
    mockGetPointsRules.mockImplementation(() => new Promise(() => {}));

    render(<PointsRules />);

    expect(screen.getByText("积分获取说明")).toBeInTheDocument();
    expect(screen.getAllByTestId("skeleton")).toHaveLength(3);
  });

  it("应该显示默认积分规则", async () => {
    mockGetPointsRules.mockResolvedValue({
      success: false,
      message: "获取失败",
    });

    render(<PointsRules />);

    await waitFor(() => {
      expect(screen.getByText("获得积分")).toBeInTheDocument();
      expect(screen.getByText("消费积分")).toBeInTheDocument();
      expect(screen.getByText("等级系统")).toBeInTheDocument();
    });

    // 检查默认规则内容
    expect(screen.getByText(/注册账户：\+10分/)).toBeInTheDocument();
    expect(screen.getByText(/验证邮箱：\+5分/)).toBeInTheDocument();
    expect(screen.getByText(/悬赏求助：-10~100分/)).toBeInTheDocument();
    expect(screen.getByText(/新手：0-99分/)).toBeInTheDocument();
  });

  it("应该显示后端返回的积分规则", async () => {
    const mockRules = {
      earn_rules: [
        { action: "测试获得", points: 15 },
        { action: "测试奖励", points: 25 },
      ],
      spend_rules: [{ action: "测试消费", points: 30 }],
      levels: [{ name: "测试等级", range: "0-50分" }],
    };

    mockGetPointsRules.mockResolvedValue({
      success: true,
      data: mockRules,
    });

    render(<PointsRules />);

    await waitFor(() => {
      expect(screen.getByText("测试获得：+15分")).toBeInTheDocument();
      expect(screen.getByText("测试奖励：+25分")).toBeInTheDocument();
      expect(screen.getByText("测试消费：-30分")).toBeInTheDocument();
      expect(screen.getByText("测试等级：0-50分")).toBeInTheDocument();
    });
  });

  it("应该显示错误状态", async () => {
    mockGetPointsRules.mockResolvedValue({
      success: false,
      message: "网络错误",
    });

    render(<PointsRules />);

    await waitFor(() => {
      expect(screen.getByText("网络错误")).toBeInTheDocument();
    });
  });

  it("应该处理网络异常", async () => {
    mockGetPointsRules.mockRejectedValue(new Error("网络异常"));

    render(<PointsRules />);

    await waitFor(() => {
      expect(screen.getByText("网络错误，请稍后重试")).toBeInTheDocument();
    });
  });
});
