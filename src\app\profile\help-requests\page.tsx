"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getMyHelpRequests } from "@/services/profileService";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PageContainer } from "@/components/layout/PageContainer";
import AuthGuard from "@/components/AuthGuard";
import {
  ArrowLeft,
  HelpCircle,
  AlertCircle,
  RefreshCw,
  Calendar,
  MessageSquare,
  Award,
  Clock,
  CheckCircle,
  XCircle,
} from "lucide-react";

interface HelpRequest {
  id: number;
  title: string;
  description: string;
  status: string;
  created_at: string;
  updated_at: string;
  answers_count: number;
  points_reward: number;
  is_urgent: boolean;
  is_resolved: boolean;
}

export default function MyHelpRequestsPage() {
  const router = useRouter();

  const [requests, setRequests] = useState<HelpRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const pageSize = 10;

  useEffect(() => {
    loadHelpRequests(currentPage);
  }, [currentPage]);

  const loadHelpRequests = async (page: number) => {
    try {
      setLoading(true);
      setError(null);

      const result = await getMyHelpRequests(page, pageSize);

      if (result.success && result.data) {
        // 确保 items 是数组，如果不是则使用空数组
        const items = Array.isArray(result.data.items) ? result.data.items : [];
        setRequests(items);
        setTotalPages(result.data.total_pages || 1);
        setTotal(result.data.total || 0);
      } else {
        // 出错时确保设置为空数组
        setRequests([]);
        setError(result.message || "获取求助列表失败");
      }
    } catch (err) {
      // 异常时确保设置为空数组
      setRequests([]);
      setError("网络错误，请稍后重试");
      console.error("加载求助列表失败:", err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "open":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "resolved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case "open":
        return "进行中";
      case "resolved":
        return "已解决";
      case "closed":
        return "已关闭";
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading && (!requests || requests.length === 0)) {
    return (
      <AuthGuard>
        <PageContainer>
          <div className="min-h-[60vh] flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">加载中...</p>
            </div>
          </div>
        </PageContainer>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <PageContainer>
        <div className="py-8">
          {/* 页面标题和导航 */}
          <div className="mb-8">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>

            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-display font-bold flex items-center">
                  <HelpCircle className="h-8 w-8 mr-3 text-blue-600" />
                  我的求助
                </h1>
                <p className="text-muted-foreground mt-2">
                  管理您发起的求助请求
                  {total > 0 && <span className="ml-2">（共 {total} 条）</span>}
                </p>
              </div>

              <Button onClick={() => router.push("/help-requests/new")}>
                发起新求助
              </Button>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>{error}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => loadHelpRequests(currentPage)}
                  className="ml-4"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重试
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* 求助列表 */}
          {(!requests || requests.length === 0) && !loading ? (
            <Card>
              <CardContent className="py-12">
                <div className="text-center">
                  <HelpCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-semibold mb-2">暂无求助记录</h3>
                  <p className="text-muted-foreground mb-6">
                    您还没有发起过求助请求
                  </p>
                  <Button onClick={() => router.push("/help-requests/new")}>
                    发起第一个求助
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {(requests || []).map((request) => (
                <Card
                  key={request.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold hover:text-blue-600 cursor-pointer">
                            {request.title}
                          </h3>

                          <span
                            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                              request.status
                            )}`}
                          >
                            {request.is_resolved ? (
                              <CheckCircle className="h-3 w-3 mr-1" />
                            ) : request.status === "closed" ? (
                              <XCircle className="h-3 w-3 mr-1" />
                            ) : (
                              <Clock className="h-3 w-3 mr-1" />
                            )}
                            {getStatusText(request.status)}
                          </span>

                          {request.is_urgent && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                              加急
                            </span>
                          )}
                        </div>

                        <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                          {request.description}
                        </p>

                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {formatDate(request.created_at)}
                          </div>

                          <div className="flex items-center">
                            <MessageSquare className="h-4 w-4 mr-1" />
                            {request.answers_count} 个回答
                          </div>

                          {request.points_reward > 0 && (
                            <div className="flex items-center">
                              <Award className="h-4 w-4 mr-1" />
                              {request.points_reward} 积分悬赏
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="ml-4 flex flex-col space-y-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            router.push(`/help-requests/${request.id}`)
                          }
                        >
                          查看详情
                        </Button>

                        {!request.is_resolved && request.status === "open" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              router.push(`/help-requests/${request.id}/edit`)
                            }
                          >
                            编辑
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={() =>
                        setCurrentPage(Math.max(1, currentPage - 1))
                      }
                      disabled={currentPage === 1 || loading}
                    >
                      上一页
                    </Button>

                    <span className="flex items-center px-4 py-2 text-sm">
                      第 {currentPage} 页，共 {totalPages} 页
                    </span>

                    <Button
                      variant="outline"
                      onClick={() =>
                        setCurrentPage(Math.min(totalPages, currentPage + 1))
                      }
                      disabled={currentPage === totalPages || loading}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </PageContainer>
    </AuthGuard>
  );
}
