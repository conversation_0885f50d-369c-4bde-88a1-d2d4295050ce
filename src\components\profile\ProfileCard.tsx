"use client";

import React, { useState, useEffect } from "react";
import { UserProfile } from "@/services/profileService";
import { getMyProfile } from "@/services/profileService";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import { Skeleton } from "@/components/ui/skeleton";
import { User, Mail, Calendar, Award } from "lucide-react";

export function ProfileCard() {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProfile();
  }, []);

  // 获取角色显示文本
  const getRoleText = (role: any) => {
    if (!role) return "普通用户";

    const roleMap: { [key: string]: string } = {
      admin: "管理员",
      moderator: "版主",
      user: "普通用户",
    };

    return role.display_name || roleMap[role.name] || role.name || "普通用户";
  };

  const loadProfile = async () => {
    setLoading(true);

    console.log("📋 ProfileCard: 开始获取个人信息");
    const result = await getMyProfile();
    console.log("📋 ProfileCard: 获取结果", result);

    if (result.success && result.data) {
      console.log("📋 ProfileCard: 设置profile数据", result.data);
      setProfile(result.data);
    }

    setLoading(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <div className="flex items-center space-x-4">
            <Skeleton className="h-16 w-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!profile) {
    console.log("📋 ProfileCard: profile为空，不渲染");
    return null;
  }

  console.log("📋 ProfileCard: 开始渲染，profile数据:", {
    username: profile.username,
    nickname: profile.nickname,
    role: profile.role,
    title: profile.title,
    points: profile.points,
    created_at: profile.created_at,
    last_login_at: profile.last_login_at,
  });

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage
                src={profile.avatar || undefined}
                alt={profile.username || "用户头像"}
              />
              <AvatarFallback>
                {profile.nickname?.charAt(0) ||
                  profile.username?.charAt(0)?.toUpperCase() ||
                  "?"}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-xl truncate whitespace-nowrap">
                {profile.nickname || profile.username || "未知用户"}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                @{profile.username || "unknown"}
              </p>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{profile.email || "未设置邮箱"}</span>
            </div>

            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{getRoleText(profile.role)}</span>
            </div>

            <div className="flex items-center space-x-2">
              <Award className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{profile.title || "无等级"}</span>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                注册于{" "}
                {profile.created_at ? formatDate(profile.created_at) : "未知"}
              </span>
            </div>

            {profile.last_login_at && (
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  最后登录 {formatDate(profile.last_login_at)}
                </span>
              </div>
            )}

            <div className="flex items-center space-x-2">
              <Award className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">
                {profile.points || 0} 积分
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
