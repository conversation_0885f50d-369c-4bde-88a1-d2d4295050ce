/**
 * PointsHistoryPage 组件测试
 * 验证积分历史页面的正确渲染和功能
 */

import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useRouter } from "next/navigation";
import PointsHistoryPage from "@/app/profile/points/page";

// Mock next/navigation
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
}));

// Mock AuthGuard
vi.mock("@/components/AuthGuard", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-guard">{children}</div>
  ),
}));

// Mock PageContainer
vi.mock("@/components/layout/PageContainer", () => ({
  PageContainer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="page-container">{children}</div>
  ),
}));

// Mock PointsHistory
vi.mock("@/components/profile/PointsHistory", () => ({
  PointsHistory: () => <div data-testid="points-history">积分历史组件</div>,
}));

// Mock lucide-react icons
vi.mock("lucide-react", () => ({
  ArrowLeft: () => <span data-testid="arrow-left-icon">←</span>,
  Award: () => <span data-testid="award-icon">🏆</span>,
  TrendingUp: () => <span data-testid="trending-up-icon">📈</span>,
  Info: () => <span data-testid="info-icon">ℹ️</span>,
}));

describe("PointsHistoryPage", () => {
  const mockRouter = {
    back: vi.fn(),
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as any).mockReturnValue(mockRouter);
  });

  it("应该正确渲染页面结构", () => {
    render(<PointsHistoryPage />);

    // 验证 AuthGuard 包装器存在
    expect(screen.getByTestId("auth-guard")).toBeInTheDocument();

    // 验证 PageContainer 存在
    expect(screen.getByTestId("page-container")).toBeInTheDocument();

    // 验证页面标题
    expect(screen.getByText("积分历史")).toBeInTheDocument();

    // 验证页面描述
    expect(screen.getByText("查看您的积分获得和消费记录")).toBeInTheDocument();

    // 验证返回按钮
    expect(screen.getByText("返回")).toBeInTheDocument();

    // 验证 PointsHistory 组件
    expect(screen.getByTestId("points-history")).toBeInTheDocument();
  });

  it("应该正确渲染图标", () => {
    render(<PointsHistoryPage />);

    // 验证各种图标存在
    expect(screen.getByTestId("arrow-left-icon")).toBeInTheDocument();
    expect(screen.getByTestId("award-icon")).toBeInTheDocument();
  });

  it("点击返回按钮应该调用 router.back()", () => {
    render(<PointsHistoryPage />);

    const backButton = screen.getByText("返回");
    fireEvent.click(backButton);

    expect(mockRouter.back).toHaveBeenCalledTimes(1);
  });

  it("应该正确应用样式类", () => {
    render(<PointsHistoryPage />);

    // 验证主容器样式
    const mainContainer = screen.getByTestId("page-container").firstChild;
    expect(mainContainer).toHaveClass("py-8");

    // 验证标题样式
    const title = screen.getByText("积分历史");
    expect(title).toHaveClass("text-3xl", "font-display", "font-bold");
  });

  it("应该正确渲染积分统计卡片", () => {
    render(<PointsHistoryPage />);

    // 验证积分统计相关文本
    expect(screen.getByText("积分统计")).toBeInTheDocument();
    expect(screen.getByText("查看您的积分获得和使用情况")).toBeInTheDocument();
  });

  it("应该正确渲染积分规则卡片", () => {
    render(<PointsHistoryPage />);

    // 验证积分规则相关文本
    expect(screen.getByText("积分规则")).toBeInTheDocument();
    expect(screen.getByText("了解如何获得和使用积分")).toBeInTheDocument();
  });

  it("应该正确处理组件导入", async () => {
    // 这个测试验证组件能够正确导入而不会抛出 "Element type is invalid" 错误
    expect(() => render(<PointsHistoryPage />)).not.toThrow();

    // 等待组件完全渲染
    await waitFor(() => {
      expect(screen.getByTestId("points-history")).toBeInTheDocument();
    });
  });
});
