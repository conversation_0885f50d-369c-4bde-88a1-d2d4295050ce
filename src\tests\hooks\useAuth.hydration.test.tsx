/**
 * 水合错误修复测试
 * 验证修复后的认证状态不会产生水合错误
 */

import React from "react";
import { describe, it, expect, beforeEach, vi } from "vitest";
import { render, screen } from "@testing-library/react";

// Mock next/navigation
vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    replace: vi.fn(),
  }),
}));

// Mock ToastProvider
vi.mock("../../components/ToastProvider", () => ({
  useToast: () => ({
    showToast: vi.fn(),
  }),
}));

// Mock authService
vi.mock("../../services/authService", () => ({
  getCurrentUser: vi.fn().mockResolvedValue(null),
  isAuthenticated: vi.fn().mockReturnValue(false),
  logout: vi.fn(),
}));

// Mock help request service
vi.mock("../../services/helpRequestService", () => ({
  getHelpRequests: vi.fn().mockResolvedValue({
    status: "success",
    data: { requests: [], pages: 1, total: 0 },
  }),
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
  writable: true,
});

// 简单的测试组件，模拟使用认证状态的场景
function AuthTestComponent() {
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(true);
  const [isMounted, setIsMounted] = React.useState(false);

  React.useEffect(() => {
    setIsMounted(true);
    // 模拟 useAuth 的逻辑
    const checkAuth = () => {
      if (typeof window !== "undefined") {
        const hasToken = !!localStorage.getItem("auth_token");
        setIsAuthenticated(hasToken);
      }
      setIsLoading(false);
    };
    checkAuth();
  }, []);

  return (
    <div>
      <div data-testid="is-authenticated">
        {isMounted ? isAuthenticated.toString() : "false"}
      </div>
      <div data-testid="is-loading">{(!isMounted || isLoading).toString()}</div>
      {isMounted && isAuthenticated && (
        <div data-testid="auth-content">认证内容</div>
      )}
    </div>
  );
}

describe("水合错误修复测试", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  it("应该在初始渲染时返回一致的状态，避免水合错误", () => {
    // 模拟有token的情况
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === "auth_token") return "test-token";
      return null;
    });

    render(<AuthTestComponent />);

    // 初始状态应该是：
    // - isAuthenticated: false (避免水合错误，因为isMounted为false)
    // - isLoading: true (因为还未挂载完成)
    expect(screen.getByTestId("is-authenticated")).toHaveTextContent("false");
    expect(screen.getByTestId("is-loading")).toHaveTextContent("true");

    // 不应该有认证内容显示
    expect(screen.queryByTestId("auth-content")).not.toBeInTheDocument();
  });

  it("应该在没有token时保持未认证状态", () => {
    // 模拟没有token的情况
    localStorageMock.getItem.mockReturnValue(null);

    render(<AuthTestComponent />);

    // 初始状态
    expect(screen.getByTestId("is-authenticated")).toHaveTextContent("false");
    expect(screen.getByTestId("is-loading")).toHaveTextContent("true");
    expect(screen.queryByTestId("auth-content")).not.toBeInTheDocument();
  });

  it("应该确保服务端和客户端渲染一致", () => {
    // 这个测试验证无论localStorage中是否有token
    // 初始渲染都应该返回相同的结果

    // 第一次渲染 - 有token
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === "auth_token") return "test-token";
      return null;
    });

    const { unmount } = render(<AuthTestComponent />);
    const firstRender = {
      isAuthenticated: screen.getByTestId("is-authenticated").textContent,
      isLoading: screen.getByTestId("is-loading").textContent,
      hasAuthContent: !!screen.queryByTestId("auth-content"),
    };

    unmount();

    // 第二次渲染 - 没有token
    localStorageMock.getItem.mockReturnValue(null);

    render(<AuthTestComponent />);
    const secondRender = {
      isAuthenticated: screen.getByTestId("is-authenticated").textContent,
      isLoading: screen.getByTestId("is-loading").textContent,
      hasAuthContent: !!screen.queryByTestId("auth-content"),
    };

    // 初始渲染应该完全一致
    expect(firstRender).toEqual(secondRender);
    expect(firstRender.isAuthenticated).toBe("false");
    expect(firstRender.isLoading).toBe("true");
    expect(firstRender.hasAuthContent).toBe(false);
  });
});
