/**
 * 个人中心统计数据测试
 * 测试总积分和用户角色的undefined处理
 */

import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import ProfilePage from "@/app/profile/page";

// Mock dependencies
const mockUseAuth = vi.fn();
const mockGetMyProfile = vi.fn();
const mockGetProfileStatistics = vi.fn();

vi.mock("@/hooks/useAuth", () => ({
  useAuth: mockUseAuth,
}));

vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
  }),
}));

vi.mock("@/services/profileService", () => ({
  getMyProfile: mockGetMyProfile,
  getProfileStatistics: mockGetProfileStatistics,
}));

describe("ProfilePage - Statistics Handling", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Mock 已登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      },
      isLoading: false,
    });
  });

  it("应该正确处理undefined的统计数据", async () => {
    // Mock profile数据
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      },
    });

    // Mock 包含undefined值的统计数据
    mockGetProfileStatistics.mockResolvedValue({
      success: true,
      data: {
        total_points: undefined,
        current_title: undefined,
        total_help_requests: undefined,
        total_help_answers: undefined,
        accepted_answers: undefined,
      },
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 检查是否显示默认值而不是undefined
      expect(screen.getByText("0")).toBeInTheDocument(); // 总积分默认值
      expect(screen.getByText("当前等级：新手")).toBeInTheDocument(); // 默认等级
    });
  });

  it("应该正确显示有效的统计数据", async () => {
    // Mock profile数据
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      },
    });

    // Mock 有效的统计数据
    mockGetProfileStatistics.mockResolvedValue({
      success: true,
      data: {
        total_points: 150,
        current_title: "活跃用户",
        total_help_requests: 5,
        total_help_answers: 10,
        accepted_answers: 3,
      },
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 检查是否显示正确的数值
      expect(screen.getByText("150")).toBeInTheDocument(); // 总积分
      expect(screen.getByText("当前等级：活跃用户")).toBeInTheDocument(); // 等级
      expect(screen.getByText("5")).toBeInTheDocument(); // 发起求助
      expect(screen.getByText("10")).toBeInTheDocument(); // 提供回答
      expect(screen.getByText("3")).toBeInTheDocument(); // 被采纳回答
    });
  });

  it("应该正确计算采纳率", async () => {
    // Mock profile数据
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      },
    });

    // Mock 统计数据
    mockGetProfileStatistics.mockResolvedValue({
      success: true,
      data: {
        total_points: 150,
        current_title: "活跃用户",
        total_help_requests: 5,
        total_help_answers: 10,
        accepted_answers: 3,
      },
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 检查采纳率计算 (3/10 * 100 = 30%)
      expect(screen.getByText("采纳率：30%")).toBeInTheDocument();
    });
  });

  it("应该处理零除法情况", async () => {
    // Mock profile数据
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      },
    });

    // Mock 统计数据 - 没有回答
    mockGetProfileStatistics.mockResolvedValue({
      success: true,
      data: {
        total_points: 50,
        current_title: "新手",
        total_help_requests: 2,
        total_help_answers: 0,
        accepted_answers: 0,
      },
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 检查采纳率为0%
      expect(screen.getByText("采纳率：0%")).toBeInTheDocument();
    });
  });
});
