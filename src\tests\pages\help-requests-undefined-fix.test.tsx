/**
 * MyHelpRequestsPage undefined 错误修复验证测试
 * 验证 "Cannot read properties of undefined (reading 'length')" 错误是否已修复
 */

import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import MyHelpRequestsPage from "@/app/profile/help-requests/page";

// Mock next/navigation
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(() => ({
    back: vi.fn(),
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  })),
}));

// Mock AuthGuard
vi.mock("@/components/AuthGuard", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-guard">{children}</div>
  ),
}));

// Mock PageContainer
vi.mock("@/components/layout/PageContainer", () => ({
  PageContainer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="page-container">{children}</div>
  ),
}));

// Mock lucide-react icons
vi.mock("lucide-react", () => ({
  ArrowLeft: () => <span data-testid="arrow-left">←</span>,
  HelpCircle: () => <span data-testid="help-circle">❓</span>,
  AlertCircle: () => <span data-testid="alert-circle">⚠️</span>,
  RefreshCw: () => <span data-testid="refresh">🔄</span>,
  Calendar: () => <span data-testid="calendar">📅</span>,
  MessageSquare: () => <span data-testid="message">💬</span>,
  Award: () => <span data-testid="award">🏆</span>,
  Clock: () => <span data-testid="clock">🕐</span>,
  CheckCircle: () => <span data-testid="check">✅</span>,
  XCircle: () => <span data-testid="x-circle">❌</span>,
}));

describe("MyHelpRequestsPage undefined 错误修复验证", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("当 getMyHelpRequests 返回 undefined items 时不应该崩溃", async () => {
    // Mock 返回 undefined items 的情况
    vi.doMock("@/services/profileService", () => ({
      getMyHelpRequests: vi.fn(() =>
        Promise.resolve({
          success: true,
          data: {
            items: undefined, // 模拟 undefined 情况
            total: 0,
            page: 1,
            size: 10,
            total_pages: 1,
          },
        })
      ),
    }));

    expect(() => {
      render(<MyHelpRequestsPage />);
    }).not.toThrow();

    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.getByTestId("auth-guard")).toBeDefined();
    });
  });

  it("当 getMyHelpRequests 返回 null data 时不应该崩溃", async () => {
    // Mock 返回 null data 的情况
    vi.doMock("@/services/profileService", () => ({
      getMyHelpRequests: vi.fn(() =>
        Promise.resolve({
          success: true,
          data: null, // 模拟 null 情况
        })
      ),
    }));

    expect(() => {
      render(<MyHelpRequestsPage />);
    }).not.toThrow();

    await waitFor(() => {
      expect(screen.getByTestId("auth-guard")).toBeDefined();
    });
  });

  it("当 getMyHelpRequests 返回错误时不应该崩溃", async () => {
    // Mock 返回错误的情况
    vi.doMock("@/services/profileService", () => ({
      getMyHelpRequests: vi.fn(() =>
        Promise.resolve({
          success: false,
          message: "获取失败",
        })
      ),
    }));

    expect(() => {
      render(<MyHelpRequestsPage />);
    }).not.toThrow();

    await waitFor(() => {
      expect(screen.getByTestId("auth-guard")).toBeDefined();
    });
  });

  it("当 getMyHelpRequests 抛出异常时不应该崩溃", async () => {
    // Mock 抛出异常的情况
    vi.doMock("@/services/profileService", () => ({
      getMyHelpRequests: vi.fn(() => Promise.reject(new Error("网络错误"))),
    }));

    expect(() => {
      render(<MyHelpRequestsPage />);
    }).not.toThrow();

    await waitFor(() => {
      expect(screen.getByTestId("auth-guard")).toBeDefined();
    });
  });

  it("当 getMyHelpRequests 返回正常数据时应该正确渲染", async () => {
    // Mock 返回正常数据的情况
    vi.doMock("@/services/profileService", () => ({
      getMyHelpRequests: vi.fn(() =>
        Promise.resolve({
          success: true,
          data: {
            items: [
              {
                id: 1,
                title: "测试求助",
                description: "这是一个测试求助",
                status: "open",
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z",
                answers_count: 0,
                points_reward: 10,
                is_urgent: false,
                is_resolved: false,
              },
            ],
            total: 1,
            page: 1,
            size: 10,
            total_pages: 1,
          },
        })
      ),
    }));

    expect(() => {
      render(<MyHelpRequestsPage />);
    }).not.toThrow();

    await waitFor(() => {
      expect(screen.getByTestId("auth-guard")).toBeDefined();
    });
  });

  it("应该正确处理空数组情况", async () => {
    // Mock 返回空数组的情况
    vi.doMock("@/services/profileService", () => ({
      getMyHelpRequests: vi.fn(() =>
        Promise.resolve({
          success: true,
          data: {
            items: [],
            total: 0,
            page: 1,
            size: 10,
            total_pages: 1,
          },
        })
      ),
    }));

    expect(() => {
      render(<MyHelpRequestsPage />);
    }).not.toThrow();

    await waitFor(() => {
      expect(screen.getByTestId("auth-guard")).toBeDefined();
    });
  });
});
