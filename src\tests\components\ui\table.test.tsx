/**
 * Table 组件测试用例
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
} from '@/components/ui/table';

describe('Table 组件', () => {
  it('应该正确渲染完整的表格结构', () => {
    render(
      <Table data-testid="table">
        <TableCaption>测试表格标题</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>列1</TableHead>
            <TableHead>列2</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>数据1</TableCell>
            <TableCell>数据2</TableCell>
          </TableRow>
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell>底部1</TableCell>
            <TableCell>底部2</TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    );

    expect(screen.getByTestId('table')).toBeInTheDocument();
    expect(screen.getByText('测试表格标题')).toBeInTheDocument();
    expect(screen.getByText('列1')).toBeInTheDocument();
    expect(screen.getByText('列2')).toBeInTheDocument();
    expect(screen.getByText('数据1')).toBeInTheDocument();
    expect(screen.getByText('数据2')).toBeInTheDocument();
    expect(screen.getByText('底部1')).toBeInTheDocument();
    expect(screen.getByText('底部2')).toBeInTheDocument();
  });

  it('应该支持自定义样式类名', () => {
    render(
      <Table className="custom-table" data-testid="table">
        <TableBody className="custom-body">
          <TableRow className="custom-row">
            <TableCell className="custom-cell">测试</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    );

    const table = screen.getByTestId('table').querySelector('table');
    expect(table).toHaveClass('custom-table');
    
    const body = screen.getByText('测试').closest('tbody');
    expect(body).toHaveClass('custom-body');
    
    const row = screen.getByText('测试').closest('tr');
    expect(row).toHaveClass('custom-row');
    
    const cell = screen.getByText('测试');
    expect(cell).toHaveClass('custom-cell');
  });

  it('应该支持 ref 转发', () => {
    const tableRef = React.createRef<HTMLTableElement>();
    const headerRef = React.createRef<HTMLTableSectionElement>();
    const bodyRef = React.createRef<HTMLTableSectionElement>();
    const footerRef = React.createRef<HTMLTableSectionElement>();
    const rowRef = React.createRef<HTMLTableRowElement>();
    const headRef = React.createRef<HTMLTableCellElement>();
    const cellRef = React.createRef<HTMLTableCellElement>();
    const captionRef = React.createRef<HTMLTableCaptionElement>();

    render(
      <Table>
        <table ref={tableRef}>
          <TableCaption ref={captionRef}>标题</TableCaption>
          <TableHeader ref={headerRef}>
            <TableRow ref={rowRef}>
              <TableHead ref={headRef}>头部</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody ref={bodyRef}>
            <TableRow>
              <TableCell ref={cellRef}>内容</TableCell>
            </TableRow>
          </TableBody>
          <TableFooter ref={footerRef}>
            <TableRow>
              <TableCell>底部</TableCell>
            </TableRow>
          </TableFooter>
        </table>
      </Table>
    );

    expect(tableRef.current).toBeInstanceOf(HTMLTableElement);
    expect(headerRef.current).toBeInstanceOf(HTMLTableSectionElement);
    expect(bodyRef.current).toBeInstanceOf(HTMLTableSectionElement);
    expect(footerRef.current).toBeInstanceOf(HTMLTableSectionElement);
    expect(rowRef.current).toBeInstanceOf(HTMLTableRowElement);
    expect(headRef.current).toBeInstanceOf(HTMLTableCellElement);
    expect(cellRef.current).toBeInstanceOf(HTMLTableCellElement);
    expect(captionRef.current).toBeInstanceOf(HTMLTableCaptionElement);
  });

  it('应该有正确的默认样式类', () => {
    render(
      <Table data-testid="table">
        <TableHeader data-testid="header">
          <TableRow data-testid="row">
            <TableHead data-testid="head">头部</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody data-testid="body">
          <TableRow>
            <TableCell data-testid="cell">内容</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    );

    const table = screen.getByTestId('table').querySelector('table');
    expect(table).toHaveClass('w-full', 'caption-bottom', 'text-sm');

    const header = screen.getByTestId('header');
    expect(header).toHaveClass('[&_tr]:border-b');

    const body = screen.getByTestId('body');
    expect(body).toHaveClass('[&_tr:last-child]:border-0');

    const row = screen.getByTestId('row');
    expect(row).toHaveClass('border-b', 'transition-colors');

    const head = screen.getByTestId('head');
    expect(head).toHaveClass('h-12', 'px-4', 'text-left', 'align-middle', 'font-medium');

    const cell = screen.getByTestId('cell');
    expect(cell).toHaveClass('p-4', 'align-middle');
  });

  it('应该支持表格的可访问性属性', () => {
    render(
      <Table>
        <table role="table" aria-label="测试表格">
          <TableHeader>
            <TableRow>
              <TableHead scope="col">列标题</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>数据</TableCell>
            </TableRow>
          </TableBody>
        </table>
      </Table>
    );

    const table = screen.getByRole('table');
    expect(table).toHaveAttribute('aria-label', '测试表格');
    
    const columnHeader = screen.getByText('列标题');
    expect(columnHeader).toHaveAttribute('scope', 'col');
  });
});
