"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

export interface SeparatorProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: "horizontal" | "vertical";
  decorative?: boolean;
}

/**
 * Separator 分隔符组件
 *
 * 用于在内容之间创建视觉分隔
 *
 * @param orientation - 分隔符方向，默认为 "horizontal"
 * @param decorative - 是否为装饰性分隔符，影响可访问性属性
 */
const Separator = React.forwardRef<HTMLDivElement, SeparatorProps>(
  (
    { className, orientation = "horizontal", decorative = true, ...props },
    ref
  ) => {
    const separatorProps = decorative
      ? { role: "none" as const }
      : {
          role: "separator" as const,
          "aria-orientation": orientation as "horizontal" | "vertical",
        };

    return (
      <div
        ref={ref}
        {...separatorProps}
        className={cn(
          "shrink-0 bg-gray-200 dark:bg-gray-700",
          orientation === "horizontal" ? "h-[1px] w-full" : "h-full w-[1px]",
          className
        )}
        {...props}
      />
    );
  }
);

Separator.displayName = "Separator";

export { Separator };
