/**
 * 汉堡栏注册登录按钮移除测试
 * 验证移除注册登录按钮后的功能
 */

import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import Navigation from "@/components/Navigation";

// Mock dependencies
vi.mock("@/hooks/useAuth", () => ({
  useAuth: vi.fn(),
}));

vi.mock("@/hooks/use-mounted", () => ({
  useMounted: () => true,
}));

vi.mock("next-themes", () => ({
  useTheme: () => ({
    theme: "light",
    setTheme: vi.fn(),
  }),
}));

vi.mock("next/navigation", () => ({
  usePathname: () => "/",
  useRouter: () => ({
    push: vi.fn(),
  }),
}));

vi.mock("@/config/features", () => ({
  shouldShowRegistrationInNav: () => true,
  shouldShowLoginInNav: () => true,
}));

import { useAuth } from "@/hooks/useAuth";

describe("Navigation - 汉堡栏注册登录按钮移除", () => {
  const mockUseAuth = vi.mocked(useAuth);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("未登录状态下汉堡栏不应该显示注册和登录按钮", () => {
    // Mock 未登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null,
      isLoading: false,
      logout: vi.fn(),
      isAdmin: () => false,
      login: vi.fn(),
      register: vi.fn(),
      checkAuth: vi.fn(),
      hasPermission: vi.fn(),
      requireAuth: vi.fn(),
      requirePermission: vi.fn(),
      isModerator: () => false,
    });

    render(<Navigation />);

    // 点击汉堡菜单按钮
    const menuButton = screen.getByRole("button", { name: /打开主菜单/i });
    fireEvent.click(menuButton);

    // 验证注册和登录按钮不存在
    expect(screen.queryByText("注册")).not.toBeInTheDocument();
    expect(screen.queryByText("登录")).not.toBeInTheDocument();

    // 验证基本导航项目仍然存在
    expect(screen.getByText("首页")).toBeInTheDocument();
    expect(screen.getByText("资源搜索")).toBeInTheDocument();
    expect(screen.getByText("教程中心")).toBeInTheDocument();
  });

  it("桌面版不应该显示注册按钮", () => {
    // Mock 未登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null,
      isLoading: false,
      logout: vi.fn(),
      isAdmin: () => false,
      login: vi.fn(),
      register: vi.fn(),
      checkAuth: vi.fn(),
      hasPermission: vi.fn(),
      requireAuth: vi.fn(),
      requirePermission: vi.fn(),
      isModerator: () => false,
    });

    render(<Navigation />);

    // 在桌面版中，注册按钮应该不存在
    expect(screen.queryByText("注册")).not.toBeInTheDocument();
  });

  it("已登录状态下汉堡栏应该正常显示其他菜单项", () => {
    // Mock 已登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      },
      isLoading: false,
      logout: vi.fn(),
      isAdmin: () => false,
      login: vi.fn(),
      register: vi.fn(),
      checkAuth: vi.fn(),
      hasPermission: vi.fn(),
      requireAuth: vi.fn(),
      requirePermission: vi.fn(),
      isModerator: () => false,
    });

    render(<Navigation />);

    // 点击汉堡菜单按钮
    const menuButton = screen.getByRole("button", { name: /打开主菜单/i });
    fireEvent.click(menuButton);

    // 验证基本导航项目存在
    expect(screen.getByText("首页")).toBeInTheDocument();
    expect(screen.getByText("资源搜索")).toBeInTheDocument();
    expect(screen.getByText("教程中心")).toBeInTheDocument();

    // 验证注册和登录按钮不存在
    expect(screen.queryByText("注册")).not.toBeInTheDocument();
    expect(screen.queryByText("登录")).not.toBeInTheDocument();
  });
});
