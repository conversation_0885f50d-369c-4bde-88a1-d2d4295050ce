/**
 * 测试积分数据映射的验证脚本
 * 用于验证后端数据结构到前端期望结构的映射是否正确
 */

// 模拟后端返回的积分历史数据
const mockPointsHistoryResponse = {
  status: "success",
  message: "获取积分历史成功",
  data: {
    total: 1,
    page: 1,
    size: 10,
    pages: 1,
    transactions: [
      {
        id: 22,
        amount: 10,
        balance_after: 20,
        transaction_type: "register",
        description: "新用户注册奖励",
        related_id: null,
        created_at: "2025-08-03T09:07:28.706153+00:00"
      }
    ]
  }
};

// 模拟后端返回的积分排行榜数据
const mockLeaderboardResponse = {
  status: "success",
  message: "获取积分排行榜成功",
  data: {
    leaderboard: [
      {
        user_id: 26,
        username: "test7629",
        nickname: "燃起来了",
        points: 20,
        title: "资源拾荒者"
      },
      {
        user_id: 28,
        username: "testwdk",
        nickname: "吃土豆不吃土豆皮",
        points: 20,
        title: "资源拾荒者"
      },
      {
        user_id: 1,
        username: "admin",
        nickname: "系统管理员",
        points: 13,
        title: "资源拾荒者"
      }
    ]
  }
};

// 模拟后端返回的积分规则数据
const mockRulesResponse = {
  status: "success",
  message: "获取积分规则成功",
  data: {
    points_rules: {
      register: {
        amount: 10,
        description: "新用户注册奖励"
      },
      help_request: {
        amount: -2,
        description: "发布求助扣费"
      },
      help_answer: {
        amount: 1,
        description: "回答问题奖励"
      },
      answer_accepted: {
        amount: 5,
        description: "回答被采纳奖励（回答者和求助者各得此分数）"
      }
    },
    title_system: {
      "资源拾荒者": {
        min_points: 0,
        max_points: 49
      },
      "云盘勘探员": {
        min_points: 50,
        max_points: 199
      },
      "链接炼金师": {
        min_points: 200,
        max_points: 499
      },
      "资源仲裁者": {
        min_points: 500,
        max_points: 999
      },
      "数据领主": {
        min_points: 1000,
        max_points: 2499
      },
      "虚拟仓鼠": {
        min_points: 2500,
        max_points: null
      }
    }
  }
};

// 测试积分历史数据映射
function testPointsHistoryMapping() {
  console.log('=== 测试积分历史数据映射 ===');
  
  const data = mockPointsHistoryResponse;
  const responseData = data.data || data;
  const transactions = responseData.transactions || responseData.items || [];
  
  const mappedItems = Array.isArray(transactions)
    ? transactions.map((transaction) => ({
        id: transaction.id,
        points: transaction.amount,
        balance_after: transaction.balance_after,
        transaction_type: transaction.transaction_type,
        description: transaction.description,
        related_id: transaction.related_id,
        created_at: transaction.created_at,
      }))
    : [];
  
  const result = {
    success: true,
    data: {
      items: mappedItems,
      total: responseData.total || 0,
      page: responseData.page || 1,
      size: responseData.size || 20,
      total_pages: responseData.pages || responseData.total_pages || 1,
    },
    message: data.message || "获取积分历史成功",
  };
  
  console.log('映射结果:', JSON.stringify(result, null, 2));
  console.log('✅ 积分历史数据映射测试通过\n');
}

// 测试积分排行榜数据映射
function testLeaderboardMapping() {
  console.log('=== 测试积分排行榜数据映射 ===');
  
  const data = mockLeaderboardResponse;
  const responseData = data.data || data;
  const leaderboardData = responseData.leaderboard || responseData || [];
  
  const mappedLeaderboard = Array.isArray(leaderboardData) 
    ? leaderboardData.map((user, index) => ({
        id: user.user_id || user.id,
        username: user.username,
        nickname: user.nickname,
        avatar: user.avatar,
        points: user.points,
        rank: index + 1,
        title: user.title,
      }))
    : [];
  
  const result = {
    success: true,
    data: mappedLeaderboard,
    message: data.message || "获取积分排行榜成功",
  };
  
  console.log('映射结果:', JSON.stringify(result, null, 2));
  console.log('✅ 积分排行榜数据映射测试通过\n');
}

// 测试积分规则数据映射
function testRulesMapping() {
  console.log('=== 测试积分规则数据映射 ===');
  
  const data = mockRulesResponse;
  const responseData = data.data || data;
  
  const mappedRules = {
    earn_rules: [],
    spend_rules: [],
    levels: [],
  };
  
  if (responseData.points_rules) {
    const pointsRules = responseData.points_rules;
    
    mappedRules.earn_rules = Object.entries(pointsRules)
      .filter(([key, rule]) => rule.amount > 0)
      .map(([key, rule]) => ({
        action: rule.description,
        points: rule.amount,
      }));
    
    mappedRules.spend_rules = Object.entries(pointsRules)
      .filter(([key, rule]) => rule.amount < 0)
      .map(([key, rule]) => ({
        action: rule.description,
        points: Math.abs(rule.amount),
      }));
  }
  
  if (responseData.title_system) {
    mappedRules.levels = Object.entries(responseData.title_system).map(
      ([title, range]) => ({
        name: title,
        range: range.max_points 
          ? `${range.min_points}-${range.max_points}分`
          : `${range.min_points}+分`,
      })
    );
  }
  
  const result = {
    success: true,
    data: mappedRules,
    message: data.message || "获取积分规则成功",
  };
  
  console.log('映射结果:', JSON.stringify(result, null, 2));
  console.log('✅ 积分规则数据映射测试通过\n');
}

// 运行所有测试
function runAllTests() {
  console.log('开始测试积分数据映射...\n');
  
  try {
    testPointsHistoryMapping();
    testLeaderboardMapping();
    testRulesMapping();
    
    console.log('🎉 所有测试通过！数据映射修复成功。');
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果直接运行此脚本
if (typeof module !== 'undefined' && require.main === module) {
  runAllTests();
}

module.exports = {
  testPointsHistoryMapping,
  testLeaderboardMapping,
  testRulesMapping,
  runAllTests
};
