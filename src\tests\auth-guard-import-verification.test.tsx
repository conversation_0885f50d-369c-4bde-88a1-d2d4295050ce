/**
 * AuthGuard 导入验证测试
 * 验证所有使用 AuthGuard 的页面都使用了正确的导入方式
 */

import React from "react";
import { describe, it, expect, vi } from "vitest";
import { render } from "@testing-library/react";

// Mock next/navigation
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(() => ({
    back: vi.fn(),
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  })),
  useSearchParams: vi.fn(() => new URLSearchParams()),
}));

// Mock useAuth hook
vi.mock("@/hooks/useAuth", () => ({
  useAuth: vi.fn(() => ({
    isLoading: false,
    isAuthenticated: true,
    user: { id: 1, name: "Test User", role: { name: "user" } },
    hasPermission: vi.fn(() => true),
    requireAuth: vi.fn(() => true),
    requirePermission: vi.fn(() => true),
    isAdmin: vi.fn(() => false),
  })),
}));

// Mock AuthGuard - 使用默认导入
vi.mock("@/components/AuthGuard", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-guard">{children}</div>
  ),
  AdminGuard: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="admin-guard">{children}</div>
  ),
}));

// Mock 其他组件
vi.mock("@/components/layout/PageContainer", () => ({
  PageContainer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="page-container">{children}</div>
  ),
}));

vi.mock("@/components/profile/PointsHistory", () => ({
  PointsHistory: () => <div data-testid="points-history">积分历史</div>,
}));

vi.mock("@/components/profile/ProfileCard", () => ({
  ProfileCard: () => <div data-testid="profile-card">个人资料卡片</div>,
}));

vi.mock("@/components/debug/ProfileDebug", () => ({
  ProfileDebug: () => <div data-testid="profile-debug">调试信息</div>,
}));

vi.mock("@/components/admin/AdminLayout", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="admin-layout">{children}</div>
  ),
}));

vi.mock("@/components/admin/AdminDashboard", () => ({
  default: () => <div data-testid="admin-dashboard">管理员面板</div>,
}));

// Mock services
vi.mock("@/services/profileService", () => ({
  getMyProfile: vi.fn(() => Promise.resolve({ success: true, data: {} })),
  getMyHelpRequests: vi.fn(() => Promise.resolve({ success: true, data: { items: [], total: 0 } })),
  getMyHelpAnswers: vi.fn(() => Promise.resolve({ success: true, data: { items: [], total: 0 } })),
  getProfileStatistics: vi.fn(() => Promise.resolve({ success: true, data: {} })),
}));

// Mock lucide-react icons
vi.mock("lucide-react", () => ({
  ArrowLeft: () => <span>←</span>,
  Award: () => <span>🏆</span>,
  TrendingUp: () => <span>📈</span>,
  Info: () => <span>ℹ️</span>,
  User: () => <span>👤</span>,
  Settings: () => <span>⚙️</span>,
  MessageSquare: () => <span>💬</span>,
  HelpCircle: () => <span>❓</span>,
  Calendar: () => <span>📅</span>,
  AlertCircle: () => <span>⚠️</span>,
  RefreshCw: () => <span>🔄</span>,
  CheckCircle: () => <span>✅</span>,
  XCircle: () => <span>❌</span>,
  Clock: () => <span>🕐</span>,
  ThumbsUp: () => <span>👍</span>,
  Star: () => <span>⭐</span>,
  Camera: () => <span>📷</span>,
}));

describe("AuthGuard 导入验证测试", () => {
  it("PointsHistoryPage 应该正确导入 AuthGuard", async () => {
    const PointsHistoryPage = (await import("@/app/profile/points/page")).default;
    
    expect(() => {
      render(<PointsHistoryPage />);
    }).not.toThrow();
  });

  it("ProfilePage 应该正确导入 AuthGuard", async () => {
    const ProfilePage = (await import("@/app/profile/page")).default;
    
    expect(() => {
      render(<ProfilePage />);
    }).not.toThrow();
  });

  it("ProfileEditPage 应该正确导入 AuthGuard", async () => {
    const ProfileEditPage = (await import("@/app/profile/edit/page")).default;
    
    expect(() => {
      render(<ProfileEditPage />);
    }).not.toThrow();
  });

  it("HelpAnswersPage 应该正确导入 AuthGuard", async () => {
    const HelpAnswersPage = (await import("@/app/profile/help-answers/page")).default;
    
    expect(() => {
      render(<HelpAnswersPage />);
    }).not.toThrow();
  });

  it("HelpRequestsPage 应该正确导入 AuthGuard", async () => {
    const HelpRequestsPage = (await import("@/app/profile/help-requests/page")).default;
    
    expect(() => {
      render(<HelpRequestsPage />);
    }).not.toThrow();
  });

  it("ProfileDebugPage 应该正确导入 AuthGuard", async () => {
    const ProfileDebugPage = (await import("@/app/debug/profile/page")).default;
    
    expect(() => {
      render(<ProfileDebugPage />);
    }).not.toThrow();
  });

  it("AdminPage 应该正确导入 AdminGuard", async () => {
    const AdminPage = (await import("@/app/admin/page")).default;
    
    expect(() => {
      render(<AdminPage />);
    }).not.toThrow();
  });

  it("MyHelpRequestsPage 应该正确导入 AuthGuard", async () => {
    const MyHelpRequestsPage = (await import("@/app/help-requests/my/page")).default;
    
    expect(() => {
      render(<MyHelpRequestsPage />);
    }).not.toThrow();
  });
});
