# 个人资料统计数据显示为0的问题排查与修复

## 问题描述

用户反馈个人资料页面的活动统计中，总积分等数据显示为0，但实际用户应该有积分数据。

## 问题排查

### 1. 数据流分析

**前端调用链**：
```
ProfilePage -> loadProfileData() -> getProfileStatistics() -> /api/profile/statistics -> 后端API
```

**涉及文件**：
- `src/app/profile/page.tsx` - 个人资料页面
- `src/services/profileService.ts` - 统计数据服务
- `src/app/api/profile/statistics/route.ts` - API路由

### 2. 可能的问题原因

#### A. 后端API问题
- 后端 `/api/profile/statistics` 端点可能返回空数据
- 数据库中用户统计数据可能未正确计算
- 后端数据结构与前端期望不一致

#### B. 前端数据处理问题
- API响应数据映射错误
- 数据类型转换问题
- 默认值处理不当

#### C. 认证问题
- Token过期或无效
- 权限不足

### 3. 调试增强

为了更好地排查问题，我添加了详细的调试日志：

**服务层调试**：
```typescript
export async function getProfileStatistics(): Promise<ProfileResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      console.log("🔐 getProfileStatistics: 未找到认证令牌");
      return { success: false, message: "未登录，请先登录" };
    }

    console.log("📊 getProfileStatistics: 开始获取统计信息...");
    const response = await fetch(`${getApiBaseUrl()}/api/profile/statistics`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    console.log("📊 getProfileStatistics: 响应状态:", response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.log("❌ getProfileStatistics: 请求失败:", errorData);
      // ... 错误处理
    }

    const data = await response.json();
    console.log("📊 getProfileStatistics: 原始响应数据:", data);

    const statisticsData = data.data || data;
    console.log("📊 getProfileStatistics: 处理后的数据:", statisticsData);

    return {
      success: true,
      message: "获取统计信息成功",
      data: statisticsData,
    };
  } catch (error) {
    console.error("❌ getProfileStatistics: 网络错误:", error);
    // ... 错误处理
  }
}
```

**页面层调试**：
```typescript
const loadProfileData = async () => {
  setLoading(true);

  console.log("🏠 ProfilePage: 开始加载统计数据...");
  const statsResult = await getProfileStatistics();
  console.log("🏠 ProfilePage: 统计数据结果:", statsResult);

  if (statsResult.success && statsResult.data) {
    console.log("✅ ProfilePage: 设置统计数据:", statsResult.data);
    setStats(statsResult.data);
  } else {
    console.log("❌ ProfilePage: 获取统计数据失败:", statsResult.message);
  }

  setLoading(false);
};
```

## 当前等级系统

根据后端返回的头衔系统数据，当前的等级划分如下：

| 等级 | 头衔 | 积分范围 | 颜色方案 |
|------|------|---------|---------|
| 1级 | 资源拾荒者 | 0-49分 | 灰色 |
| 2级 | 云盘勘探员 | 50-199分 | 蓝色 |
| 3级 | 链接炼金师 | 200-499分 | 紫色 |
| 4级 | 资源仲裁者 | 500-999分 | 绿色 |
| 5级 | 数据领主 | 1000-2499分 | 橙色 |
| 6级 | 虚拟仓鼠 | 2500+分 | 红色 |

### 等级晋升规则

- **新用户**：注册时获得10分，自动成为"资源拾荒者"
- **积分获取**：通过回答问题、被采纳等方式获得积分
- **积分消费**：发布求助等操作会消费积分
- **自动晋升**：积分达到对应范围时自动获得新头衔

## 排查步骤

### 1. 检查浏览器控制台
打开浏览器开发者工具，查看控制台输出：
- 是否有认证错误
- API请求是否成功
- 返回的数据结构是什么

### 2. 检查网络请求
在Network标签页中查看：
- `/api/profile/statistics` 请求状态
- 请求头是否包含正确的Authorization
- 响应数据的具体内容

### 3. 检查后端日志
如果前端请求正常，需要检查后端：
- 后端API是否正确处理请求
- 数据库查询是否返回正确数据
- 数据计算逻辑是否正确

## 可能的解决方案

### 1. 数据结构修复
如果后端返回的数据结构不正确，需要在前端进行适配：

```typescript
const statisticsData = {
  total_points: data.total_points || data.points || 0,
  current_title: data.current_title || data.title || "资源拾荒者",
  total_help_requests: data.total_help_requests || 0,
  total_help_answers: data.total_help_answers || 0,
  accepted_answers: data.accepted_answers || 0,
};
```

### 2. 默认值处理
确保所有统计数据都有合理的默认值：

```typescript
<StatCard
  title="总积分"
  value={stats?.total_points ?? 0}
  icon={Award}
  description={`当前等级：${stats?.current_title ?? "资源拾荒者"}`}
  onClick={() => router.push("/profile/points")}
/>
```

### 3. 错误处理增强
添加更好的错误提示和重试机制：

```typescript
if (!statsResult.success) {
  // 显示错误提示
  console.error("获取统计数据失败:", statsResult.message);
  // 可以添加重试按钮或自动重试逻辑
}
```

## 验证方法

### 1. 手动测试
1. 登录用户账号
2. 访问个人资料页面
3. 查看浏览器控制台输出
4. 检查统计数据是否正确显示

### 2. API测试
使用Postman或curl直接测试API：
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     http://localhost:3000/api/profile/statistics
```

### 3. 数据库验证
直接查询数据库，确认用户的积分数据是否存在：
```sql
-- 检查用户积分
SELECT * FROM user_points WHERE user_id = ?;

-- 检查积分历史
SELECT * FROM point_transactions WHERE user_id = ?;
```

## 总结

通过添加详细的调试日志，我们现在可以更好地追踪问题的根源。主要的排查方向包括：

1. **认证问题**：检查token是否有效
2. **API问题**：检查后端API是否正常返回数据
3. **数据映射问题**：检查前后端数据结构是否一致
4. **默认值问题**：确保有合理的默认值处理

建议按照上述步骤逐一排查，通过控制台输出可以快速定位问题所在。
