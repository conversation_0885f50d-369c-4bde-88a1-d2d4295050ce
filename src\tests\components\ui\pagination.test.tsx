/**
 * Pagination 组件测试用例
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  ChevronLeft: () => <div data-testid="chevron-left" />,
  ChevronRight: () => <div data-testid="chevron-right" />,
  MoreHorizontal: () => <div data-testid="more-horizontal" />,
}));

describe('Pagination 组件', () => {
  it('应该正确渲染完整的分页结构', () => {
    render(
      <Pagination data-testid="pagination">
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious href="#" />
          </PaginationItem>
          <PaginationItem>
            <PaginationLink href="#" isActive>
              1
            </PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationLink href="#">2</PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationEllipsis />
          </PaginationItem>
          <PaginationItem>
            <PaginationNext href="#" />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );

    expect(screen.getByTestId('pagination')).toBeInTheDocument();
    expect(screen.getByText('上一页')).toBeInTheDocument();
    expect(screen.getByText('下一页')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByTestId('more-horizontal')).toBeInTheDocument();
  });

  it('应该正确处理活跃状态', () => {
    render(
      <PaginationContent>
        <PaginationItem>
          <PaginationLink href="#" isActive data-testid="active-link">
            1
          </PaginationLink>
        </PaginationItem>
        <PaginationItem>
          <PaginationLink href="#" data-testid="inactive-link">
            2
          </PaginationLink>
        </PaginationItem>
      </PaginationContent>
    );

    const activeLink = screen.getByTestId('active-link');
    const inactiveLink = screen.getByTestId('inactive-link');

    expect(activeLink).toHaveAttribute('aria-current', 'page');
    expect(inactiveLink).not.toHaveAttribute('aria-current');

    // 检查活跃状态的样式
    expect(activeLink).toHaveClass('border-blue-500', 'bg-blue-50', 'text-blue-600');
  });

  it('应该支持点击事件', () => {
    const handleClick = vi.fn();
    
    render(
      <PaginationContent>
        <PaginationItem>
          <PaginationLink href="#" onClick={handleClick}>
            1
          </PaginationLink>
        </PaginationItem>
      </PaginationContent>
    );

    const link = screen.getByText('1');
    fireEvent.click(link);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('应该正确渲染上一页和下一页按钮', () => {
    render(
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious href="#" data-testid="prev" />
        </PaginationItem>
        <PaginationItem>
          <PaginationNext href="#" data-testid="next" />
        </PaginationItem>
      </PaginationContent>
    );

    const prevButton = screen.getByTestId('prev');
    const nextButton = screen.getByTestId('next');

    expect(prevButton).toHaveAttribute('aria-label', 'Go to previous page');
    expect(nextButton).toHaveAttribute('aria-label', 'Go to next page');

    expect(screen.getByTestId('chevron-left')).toBeInTheDocument();
    expect(screen.getByTestId('chevron-right')).toBeInTheDocument();
  });

  it('应该正确渲染省略号', () => {
    render(<PaginationEllipsis data-testid="ellipsis" />);

    const ellipsis = screen.getByTestId('ellipsis');
    expect(ellipsis).toHaveAttribute('aria-hidden');
    expect(screen.getByTestId('more-horizontal')).toBeInTheDocument();
    expect(screen.getByText('更多页面')).toBeInTheDocument();
  });

  it('应该支持自定义样式类名', () => {
    render(
      <Pagination className="custom-pagination" data-testid="pagination">
        <PaginationContent className="custom-content">
          <PaginationItem className="custom-item">
            <PaginationLink href="#" className="custom-link">
              1
            </PaginationLink>
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );

    const pagination = screen.getByTestId('pagination');
    expect(pagination).toHaveClass('custom-pagination');

    const content = screen.getByText('1').closest('ul');
    expect(content).toHaveClass('custom-content');

    const item = screen.getByText('1').closest('li');
    expect(item).toHaveClass('custom-item');

    const link = screen.getByText('1');
    expect(link).toHaveClass('custom-link');
  });

  it('应该有正确的默认样式类', () => {
    render(
      <Pagination data-testid="pagination">
        <PaginationContent data-testid="content">
          <PaginationItem data-testid="item">
            <PaginationLink href="#" data-testid="link">
              1
            </PaginationLink>
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );

    const pagination = screen.getByTestId('pagination');
    expect(pagination).toHaveClass('mx-auto', 'flex', 'w-full', 'justify-center');

    const content = screen.getByTestId('content');
    expect(content).toHaveClass('flex', 'flex-row', 'items-center', 'gap-1');

    const link = screen.getByTestId('link');
    expect(link).toHaveClass(
      'inline-flex',
      'items-center',
      'justify-center',
      'whitespace-nowrap',
      'rounded-md',
      'text-sm',
      'font-medium'
    );
  });

  it('应该支持不同的尺寸', () => {
    const { rerender } = render(
      <PaginationLink href="#" size="sm" data-testid="link">
        1
      </PaginationLink>
    );

    let link = screen.getByTestId('link');
    expect(link).toHaveClass('h-9', 'px-3');

    rerender(
      <PaginationLink href="#" size="lg" data-testid="link">
        1
      </PaginationLink>
    );

    link = screen.getByTestId('link');
    expect(link).toHaveClass('h-11', 'px-8');
  });
});
