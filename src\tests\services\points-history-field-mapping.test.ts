/**
 * 积分历史字段映射测试
 * 验证后端数据字段正确映射到前端期望的字段
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { getPointsHistory } from "@/services/profileService";

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, "localStorage", {
  value: mockLocalStorage,
  writable: true,
});

// Mock getApiBaseUrl
vi.mock("@/config/api", () => ({
  getApiBaseUrl: () => "http://localhost:3000",
}));

describe("积分历史字段映射测试", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // 设置 localStorage token
    mockLocalStorage.getItem.mockReturnValue("mock-token");
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("应该正确映射后端 transactions 字段到前端 items 字段", async () => {
    // Mock 后端返回的真实数据结构
    const mockBackendResponse = {
      status: "success",
      message: "获取积分历史成功",
      data: {
        total: 1,
        page: 1,
        size: 10,
        pages: 1,
        transactions: [
          {
            id: 22,
            amount: 10,
            balance_after: 20,
            transaction_type: "register",
            description: "新用户注册奖励",
            related_id: null,
            created_at: "2025-08-03T09:07:28.706153+00:00",
          },
        ],
      },
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockBackendResponse,
    });

    const result = await getPointsHistory(1, 10);

    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.items).toBeDefined();
    expect(Array.isArray(result.data.items)).toBe(true);
    expect(result.data.items.length).toBe(1);

    // 验证字段映射
    const item = result.data.items[0];
    expect(item.id).toBe(22);
    expect(item.points).toBe(10); // amount 映射到 points
    expect(item.balance_after).toBe(20);
    expect(item.transaction_type).toBe("register");
    expect(item.description).toBe("新用户注册奖励");
    expect(item.related_id).toBe(null);
    expect(item.created_at).toBe("2025-08-03T09:07:28.706153+00:00");

    // 验证分页信息映射
    expect(result.data.total).toBe(1);
    expect(result.data.page).toBe(1);
    expect(result.data.size).toBe(10);
    expect(result.data.total_pages).toBe(1); // pages 映射到 total_pages
  });

  it("应该处理空的 transactions 数组", async () => {
    const mockBackendResponse = {
      status: "success",
      message: "获取积分历史成功",
      data: {
        total: 0,
        page: 1,
        size: 10,
        pages: 1,
        transactions: [],
      },
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockBackendResponse,
    });

    const result = await getPointsHistory(1, 10);

    expect(result.success).toBe(true);
    expect(result.data.items).toEqual([]);
    expect(result.data.total).toBe(0);
  });

  it("应该处理缺少 transactions 字段的情况", async () => {
    const mockBackendResponse = {
      status: "success",
      message: "获取积分历史成功",
      data: {
        total: 0,
        page: 1,
        size: 10,
        pages: 1,
        // 缺少 transactions 字段
      },
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockBackendResponse,
    });

    const result = await getPointsHistory(1, 10);

    expect(result.success).toBe(true);
    expect(result.data.items).toEqual([]);
    expect(result.data.total).toBe(0);
  });

  it("应该处理多条积分记录", async () => {
    const mockBackendResponse = {
      status: "success",
      message: "获取积分历史成功",
      data: {
        total: 3,
        page: 1,
        size: 10,
        pages: 1,
        transactions: [
          {
            id: 1,
            amount: 10,
            balance_after: 10,
            transaction_type: "register",
            description: "新用户注册奖励",
            related_id: null,
            created_at: "2025-08-01T09:07:28.706153+00:00",
          },
          {
            id: 2,
            amount: 5,
            balance_after: 15,
            transaction_type: "answer",
            description: "回答问题奖励",
            related_id: 123,
            created_at: "2025-08-02T09:07:28.706153+00:00",
          },
          {
            id: 3,
            amount: -3,
            balance_after: 12,
            transaction_type: "ask",
            description: "提问消费",
            related_id: 456,
            created_at: "2025-08-03T09:07:28.706153+00:00",
          },
        ],
      },
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockBackendResponse,
    });

    const result = await getPointsHistory(1, 10);

    expect(result.success).toBe(true);
    expect(result.data.items.length).toBe(3);

    // 验证正积分记录
    expect(result.data.items[0].points).toBe(10);
    expect(result.data.items[1].points).toBe(5);

    // 验证负积分记录
    expect(result.data.items[2].points).toBe(-3);

    // 验证所有字段都正确映射
    result.data.items.forEach((item) => {
      expect(item).toHaveProperty("id");
      expect(item).toHaveProperty("points");
      expect(item).toHaveProperty("balance_after");
      expect(item).toHaveProperty("transaction_type");
      expect(item).toHaveProperty("description");
      expect(item).toHaveProperty("related_id");
      expect(item).toHaveProperty("created_at");
    });
  });

  it("应该处理网络错误", async () => {
    mockFetch.mockRejectedValueOnce(new Error("Network error"));

    const result = await getPointsHistory(1, 10);

    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });

  it("应该处理 HTTP 错误状态", async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
      statusText: "Internal Server Error",
    });

    const result = await getPointsHistory(1, 10);

    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });
});
