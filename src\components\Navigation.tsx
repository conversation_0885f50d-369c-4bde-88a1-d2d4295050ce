"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";
import { SunIcon, MoonIcon } from "@heroicons/react/24/solid";
import { useMounted } from "@/hooks/use-mounted";
import { useAuth } from "@/hooks/useAuth";
import {
  shouldShowRegistrationInNav,
  shouldShowLoginInNav,
} from "@/config/features";
import PersonalMenu from "@/components/PersonalMenu";

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const mounted = useMounted();
  const { theme, setTheme } = useTheme();
  const { isAuthenticated } = useAuth();

  // 主题切换函数
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // 点击菜单外区域关闭菜单
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // 检查点击的元素是否在移动端菜单外
      if (
        isOpen &&
        !target.closest(".mobile-menu") &&
        !target.closest(".menu-button")
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <nav
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled ? "glass-effect shadow-sm nav-scrolled" : "nav-transparent"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-12 md:h-14">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Link
                href="/"
                className="text-lg md:text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-purple-600 hover:opacity-80 transition-opacity"
              >
                97盘搜
              </Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link
                  href="/"
                  className="px-3 py-2 rounded-lg text-sm font-medium text-foreground hover:bg-hover-background transition-colors"
                >
                  首页
                </Link>
                <Link
                  href="/search"
                  className="px-3 py-2 rounded-lg text-sm font-medium text-foreground hover:bg-hover-background transition-colors"
                >
                  资源搜索
                </Link>
                <Link
                  href="/tutorials"
                  className="px-3 py-2 rounded-lg text-sm font-medium text-foreground hover:bg-hover-background transition-colors"
                >
                  教程中心
                </Link>
                <Link
                  href="/help-requests"
                  className="px-3 py-2 rounded-lg text-sm font-medium text-foreground hover:bg-hover-background transition-colors"
                >
                  资源求助
                </Link>
                <Link
                  href="/submit"
                  className="px-3 py-2 rounded-lg text-sm font-medium text-foreground hover:bg-hover-background transition-colors"
                >
                  资源提交
                </Link>
                <a
                  href="https://qm.qq.com/q/Ehs0DoCFig"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-3 py-2 rounded-lg text-sm font-medium text-foreground hover:bg-hover-background transition-colors"
                >
                  官方群
                </a>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            {/* 主题切换按钮 */}
            {mounted ? (
              <button
                type="button"
                onClick={toggleTheme}
                className="p-2 rounded-full hover:bg-hover-background focus:outline-none transition-colors"
                aria-label="切换Dark/白天模式"
              >
                {theme === "dark" ? (
                  <SunIcon className="h-5 w-5 text-foreground" />
                ) : (
                  <MoonIcon className="h-5 w-5 text-foreground" />
                )}
              </button>
            ) : (
              <div className="w-9 h-9" />
            )}

            {/* 用户认证区域 - 桌面版 */}
            <div className="hidden md:flex items-center space-x-1">
              {/* 个人菜单 - 桌面版 */}
              <PersonalMenu />
            </div>
            {/* 移动端右侧按钮区域 */}
            <div className="md:hidden flex items-center space-x-1">
              {/* 个人菜单 - 移动端 */}
              <PersonalMenu />

              {/* 汉堡菜单按钮 */}
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsOpen(!isOpen);
                }}
                className="menu-button inline-flex items-center justify-center p-2 rounded-lg hover:bg-hover-background focus:outline-none transition-colors"
                aria-label="打开主菜单"
              >
                {isOpen ? (
                  <XMarkIcon className="h-5 w-5 text-foreground" />
                ) : (
                  <Bars3Icon className="h-5 w-5 text-foreground" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 移动端菜单 */}
      {isOpen && (
        <div className="mobile-menu md:hidden glass-effect border-t border-border-color">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <Link
              href="/"
              className="block px-3 py-2 rounded-lg text-base font-medium text-foreground hover:bg-hover-background transition-colors"
              onClick={() => setIsOpen(false)}
            >
              首页
            </Link>
            <Link
              href="/search"
              className="block px-3 py-2 rounded-lg text-base font-medium text-foreground hover:bg-hover-background transition-colors"
              onClick={() => setIsOpen(false)}
            >
              资源搜索
            </Link>
            <Link
              href="/tutorials"
              className="block px-3 py-2 rounded-lg text-base font-medium text-foreground hover:bg-hover-background transition-colors"
              onClick={() => setIsOpen(false)}
            >
              教程中心
            </Link>
            <Link
              href="/help-requests"
              className="block px-3 py-2 rounded-lg text-base font-medium text-foreground hover:bg-hover-background transition-colors"
              onClick={() => setIsOpen(false)}
            >
              资源求助
            </Link>
            <Link
              href="/submit"
              className="block px-3 py-2 rounded-lg text-base font-medium text-foreground hover:bg-hover-background transition-colors"
              onClick={() => setIsOpen(false)}
            >
              资源提交
            </Link>
            <a
              href="https://qm.qq.com/q/Ehs0DoCFig"
              target="_blank"
              rel="noopener noreferrer"
              className="block px-3 py-2 rounded-lg text-base font-medium text-foreground hover:bg-hover-background transition-colors"
              onClick={() => setIsOpen(false)}
            >
              官方群
            </a>
          </div>
        </div>
      )}
    </nav>
  );
}
