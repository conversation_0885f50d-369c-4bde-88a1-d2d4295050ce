import React from "react";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import { PointsHistory } from "@/components/profile/PointsHistory";
import { getPointsHistory } from "@/services/profileService";

// Mock the service
vi.mock("@/services/profileService", () => ({
  getPointsHistory: vi.fn(),
}));

const mockGetPointsHistory = vi.mocked(getPointsHistory);

describe("PointsHistory", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该显示加载状态", () => {
    mockGetPointsHistory.mockImplementation(() => new Promise(() => {}));

    render(<PointsHistory />);

    expect(screen.getByText("积分历史")).toBeInTheDocument();
    // 检查是否有加载状态的骨架屏
    expect(screen.getByText("积分历史")).toBeInTheDocument();
  });

  it("应该显示积分历史数据", async () => {
    const mockHistory = {
      success: true,
      data: {
        items: [
          {
            id: 1,
            points: 10,
            transaction_type: "register",
            description: "新用户注册奖励",
            created_at: "2025-08-03T09:07:28.706153+00:00",
          },
          {
            id: 2,
            points: -2,
            transaction_type: "help_request",
            description: "发布求助扣费",
            created_at: "2025-08-02T10:00:00.000000+00:00",
          },
        ],
        total: 2,
        page: 1,
        size: 10,
        total_pages: 1,
      },
    };

    mockGetPointsHistory.mockResolvedValue(mockHistory);

    render(<PointsHistory />);

    await waitFor(() => {
      expect(screen.getByText("新用户注册奖励")).toBeInTheDocument();
      expect(screen.getByText("发布求助扣费")).toBeInTheDocument();
    });

    // 检查积分显示
    expect(screen.getByText("+10")).toBeInTheDocument();
    expect(screen.getByText("-2")).toBeInTheDocument();
    
    // 检查是否显示总记录数
    expect(screen.getByText("已显示全部 2 条记录")).toBeInTheDocument();
  });

  it("应该显示空状态", async () => {
    mockGetPointsHistory.mockResolvedValue({
      success: true,
      data: {
        items: [],
        total: 0,
        page: 1,
        size: 10,
        total_pages: 0,
      },
    });

    render(<PointsHistory />);

    await waitFor(() => {
      expect(screen.getByText("暂无积分记录")).toBeInTheDocument();
    });
  });

  it("应该显示错误状态", async () => {
    mockGetPointsHistory.mockResolvedValue({
      success: false,
      message: "获取积分历史失败",
      data: {
        items: [],
        total: 0,
        page: 1,
        size: 10,
        total_pages: 0,
      },
    });

    render(<PointsHistory />);

    await waitFor(() => {
      expect(screen.getByText("获取积分历史失败")).toBeInTheDocument();
    });
  });

  it("应该处理网络异常", async () => {
    mockGetPointsHistory.mockRejectedValue(new Error("网络异常"));

    render(<PointsHistory />);

    await waitFor(() => {
      expect(screen.getByText("网络错误，请稍后重试")).toBeInTheDocument();
    });
  });

  it("应该支持加载更多功能", async () => {
    // 第一页数据
    const firstPageData = {
      success: true,
      data: {
        items: [
          {
            id: 1,
            points: 10,
            transaction_type: "register",
            description: "第一页数据",
            created_at: "2025-08-03T09:07:28.706153+00:00",
          },
        ],
        total: 2,
        page: 1,
        size: 10,
        total_pages: 2,
      },
    };

    // 第二页数据
    const secondPageData = {
      success: true,
      data: {
        items: [
          {
            id: 2,
            points: 5,
            transaction_type: "answer",
            description: "第二页数据",
            created_at: "2025-08-02T09:07:28.706153+00:00",
          },
        ],
        total: 2,
        page: 2,
        size: 10,
        total_pages: 2,
      },
    };

    mockGetPointsHistory
      .mockResolvedValueOnce(firstPageData)
      .mockResolvedValueOnce(secondPageData);

    render(<PointsHistory />);

    // 等待第一页数据加载
    await waitFor(() => {
      expect(screen.getByText("第一页数据")).toBeInTheDocument();
    });

    // 检查是否有"加载更多"按钮
    const loadMoreButton = screen.getByText("加载更多");
    expect(loadMoreButton).toBeInTheDocument();

    // 点击加载更多
    fireEvent.click(loadMoreButton);

    // 等待第二页数据加载
    await waitFor(() => {
      expect(screen.getByText("第二页数据")).toBeInTheDocument();
    });

    // 检查两页数据都存在
    expect(screen.getByText("第一页数据")).toBeInTheDocument();
    expect(screen.getByText("第二页数据")).toBeInTheDocument();

    // 检查是否显示全部记录
    expect(screen.getByText("已显示全部 2 条记录")).toBeInTheDocument();
  });

  it("应该支持刷新功能", async () => {
    const mockHistory = {
      success: true,
      data: {
        items: [
          {
            id: 1,
            points: 10,
            transaction_type: "register",
            description: "测试数据",
            created_at: "2025-08-03T09:07:28.706153+00:00",
          },
        ],
        total: 1,
        page: 1,
        size: 10,
        total_pages: 1,
      },
    };

    mockGetPointsHistory.mockResolvedValue(mockHistory);

    render(<PointsHistory />);

    await waitFor(() => {
      expect(screen.getByText("测试数据")).toBeInTheDocument();
    });

    // 点击刷新按钮
    const refreshButton = screen.getByText("刷新");
    fireEvent.click(refreshButton);

    // 验证服务被再次调用
    await waitFor(() => {
      expect(mockGetPointsHistory).toHaveBeenCalledTimes(2);
    });
  });

  it("应该正确显示积分类型", async () => {
    const mockHistory = {
      success: true,
      data: {
        items: [
          {
            id: 1,
            points: 10,
            transaction_type: "register",
            description: "注册奖励",
            created_at: "2025-08-03T09:07:28.706153+00:00",
          },
        ],
        total: 1,
        page: 1,
        size: 10,
        total_pages: 1,
      },
    };

    mockGetPointsHistory.mockResolvedValue(mockHistory);

    render(<PointsHistory />);

    await waitFor(() => {
      expect(screen.getByText("注册奖励")).toBeInTheDocument();
      expect(screen.getByText("+10")).toBeInTheDocument();
    });

    // 检查是否有正确的图标（正数应该显示上升趋势图标）
    const trendingUpIcon = screen.getByText("+10").closest("div")?.querySelector("svg");
    expect(trendingUpIcon).toBeInTheDocument();
  });
});
