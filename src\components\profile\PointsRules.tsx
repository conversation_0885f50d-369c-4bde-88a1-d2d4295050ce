"use client";

import React, { useState, useEffect } from "react";
import { getPointsRules } from "@/services/profileService";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info, AlertCircle, Plus, Minus } from "lucide-react";

interface PointsRulesProps {
  className?: string;
}

export function PointsRules({ className }: PointsRulesProps) {
  const [rules, setRules] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPointsRules();
  }, []);

  const loadPointsRules = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getPointsRules();

      if (result.success && result.data) {
        setRules(result.data);
      } else {
        setError(result.message || "获取积分规则失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
      console.error("加载积分规则失败:", err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Info className="h-5 w-5 mr-2" />
            积分获取说明
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Info className="h-5 w-5 mr-2" />
            积分获取说明
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // 默认积分规则（如果后端没有返回数据）
  const defaultRules = {
    earn_rules: [
      { action: "注册账户", points: 10 },
      { action: "验证邮箱", points: 5 },
      { action: "发布求助", points: 2 },
      { action: "回答问题", points: 5 },
      { action: "回答被采纳", points: 20 },
      { action: "每日签到", points: 1 },
    ],
    spend_rules: [
      { action: "悬赏求助", points: "10~100" },
      { action: "置顶求助", points: 50 },
      { action: "加急处理", points: 20 },
    ],
    levels: [
      { name: "新手", range: "0-99分" },
      { name: "活跃用户", range: "100-499分" },
      { name: "资深用户", range: "500-999分" },
      { name: "专家用户", range: "1000+分" },
    ],
  };

  const displayRules = rules || defaultRules;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Info className="h-5 w-5 mr-2" />
          积分获取说明
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 获得积分 */}
        {displayRules.earn_rules && (
          <div>
            <h4 className="font-medium text-green-600 dark:text-green-400 mb-2 flex items-center">
              <Plus className="h-4 w-4 mr-1" />
              获得积分
            </h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {displayRules.earn_rules.map((rule: any, index: number) => (
                <li key={index}>
                  • {rule.action}：+{rule.points}分
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 消费积分 */}
        {displayRules.spend_rules && (
          <div>
            <h4 className="font-medium text-red-600 dark:text-red-400 mb-2 flex items-center">
              <Minus className="h-4 w-4 mr-1" />
              消费积分
            </h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {displayRules.spend_rules.map((rule: any, index: number) => (
                <li key={index}>
                  • {rule.action}：-{rule.points}分
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 等级系统 */}
        {displayRules.levels && (
          <div className="pt-4 border-t">
            <h4 className="font-medium text-blue-600 dark:text-blue-400 mb-2">
              等级系统
            </h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {displayRules.levels.map((level: any, index: number) => (
                <li key={index}>
                  • {level.name}：{level.range}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
