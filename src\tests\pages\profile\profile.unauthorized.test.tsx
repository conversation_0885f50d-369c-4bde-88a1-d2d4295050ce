/**
 * 个人中心未登录状态测试
 * 测试未登录时的提示和自动跳转功能
 */

import React from "react";
import { render, screen, waitFor, act } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
import ProfilePage from "@/app/profile/page";

// Mock dependencies
const mockUseAuth = vi.fn();
const mockPush = vi.fn();

vi.mock("@/hooks/useAuth", () => ({
  useAuth: mockUseAuth,
}));

vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: mockPush,
    back: vi.fn(),
  }),
}));

vi.mock("@/services/profileService", () => ({
  getMyProfile: vi.fn(),
  getProfileStatistics: vi.fn(),
}));

describe("ProfilePage - Unauthorized Access", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it("应该显示未登录提示", async () => {
    // Mock 未登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null,
      isLoading: false,
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 检查未登录提示
      expect(screen.getByText("未登录")).toBeInTheDocument();
      expect(
        screen.getByText("您需要登录后才能查看个人中心")
      ).toBeInTheDocument();
      expect(
        screen.getByText("3.5秒后将自动跳转到登录页面...")
      ).toBeInTheDocument();
    });
  });

  it("应该显示立即登录按钮", async () => {
    // Mock 未登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null,
      isLoading: false,
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 检查立即登录按钮
      const loginButton = screen.getByRole("button", { name: "立即登录" });
      expect(loginButton).toBeInTheDocument();
    });
  });

  it("应该在3.5秒后自动跳转到登录页面", async () => {
    // Mock 未登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null,
      isLoading: false,
    });

    render(<ProfilePage />);

    // 等待组件渲染
    await waitFor(() => {
      expect(screen.getByText("未登录")).toBeInTheDocument();
    });

    // 快进3.5秒
    act(() => {
      vi.advanceTimersByTime(3500);
    });

    // 检查是否调用了路由跳转
    expect(mockPush).toHaveBeenCalledWith("/login?redirect=%2Fprofile");
  });

  it("应该在点击立即登录按钮时跳转", async () => {
    // Mock 未登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null,
      isLoading: false,
    });

    render(<ProfilePage />);

    await waitFor(() => {
      const loginButton = screen.getByRole("button", { name: "立即登录" });
      loginButton.click();
    });

    // 检查是否调用了路由跳转
    expect(mockPush).toHaveBeenCalledWith("/login?redirect=%2Fprofile");
  });

  it("应该在已登录时正常显示页面内容", async () => {
    // Mock 已登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      },
      isLoading: false,
    });

    // Mock 服务调用
    const mockGetMyProfile = vi.mocked(
      require("@/services/profileService").getMyProfile
    );
    const mockGetProfileStatistics = vi.mocked(
      require("@/services/profileService").getProfileStatistics
    );

    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      },
    });

    mockGetProfileStatistics.mockResolvedValue({
      success: true,
      data: {
        total_points: 100,
        current_title: "活跃用户",
        total_help_requests: 5,
        total_help_answers: 10,
        accepted_answers: 3,
      },
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 检查页面标题
      expect(screen.getByText("个人资料")).toBeInTheDocument();
      expect(screen.getByText("查看和管理您的个人信息")).toBeInTheDocument();

      // 不应该显示未登录提示
      expect(screen.queryByText("未登录")).not.toBeInTheDocument();
    });
  });
});
