"use client";

import React, { useState, useEffect } from "react";
import { getMyProfile, UserProfile } from "@/services/profileService";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export function ProfileDebug() {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [rawData, setRawData] = useState<any>(null);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log("🔍 ProfileDebug: 开始获取个人信息");
      const result = await getMyProfile();
      console.log("🔍 ProfileDebug: 获取结果", result);

      if (result.success && result.data) {
        setProfile(result.data);
        setRawData(result.data);
        console.log("🔍 ProfileDebug: 设置的profile数据", result.data);
      } else {
        setError(result.message || "获取个人信息失败");
        console.error("🔍 ProfileDebug: 获取失败", result);
      }
    } catch (err) {
      console.error("🔍 ProfileDebug: 异常", err);
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>调试信息 - 加载中...</CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>调试信息 - 错误</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600">{error}</p>
          <button onClick={loadProfile} className="mt-2 px-4 py-2 bg-blue-500 text-white rounded">
            重试
          </button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>调试信息 - 个人资料数据</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">解析后的 Profile 对象:</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(profile, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">原始数据:</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(rawData, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">关键字段检查:</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>用户名:</strong> {profile?.username || "未设置"}
              </div>
              <div>
                <strong>昵称:</strong> {profile?.nickname || "未设置"}
              </div>
              <div>
                <strong>角色名称:</strong> {profile?.role?.name || "未设置"}
              </div>
              <div>
                <strong>角色显示名:</strong> {profile?.role?.display_name || "未设置"}
              </div>
              <div>
                <strong>头衔:</strong> {profile?.title || "未设置"}
              </div>
              <div>
                <strong>积分:</strong> {profile?.points || 0}
              </div>
              <div>
                <strong>注册时间:</strong> {profile?.created_at || "未设置"}
              </div>
              <div>
                <strong>最后登录:</strong> {profile?.last_login_at || "未设置"}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
