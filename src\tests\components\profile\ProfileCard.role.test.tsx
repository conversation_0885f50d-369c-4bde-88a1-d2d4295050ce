/**
 * ProfileCard用户角色显示测试
 * 测试用户角色undefined的处理
 */

import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { ProfileCard } from "@/components/profile/ProfileCard";

// Mock dependencies
const mockGetMyProfile = vi.fn();

vi.mock("@/services/profileService", () => ({
  getMyProfile: mockGetMyProfile,
}));

describe("ProfileCard - Role Display", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该正确显示用户角色", async () => {
    // Mock 有效的用户数据
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: {
          name: "admin",
          display_name: "管理员",
        },
        title: "活跃用户",
      },
    });

    render(<ProfileCard />);

    await waitFor(() => {
      // 检查是否显示正确的角色
      expect(screen.getByText("管理员")).toBeInTheDocument();
    });
  });

  it("应该处理role为undefined的情况", async () => {
    // Mock role为undefined的用户数据
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: undefined,
        title: "新手",
      },
    });

    render(<ProfileCard />);

    await waitFor(() => {
      // 检查是否显示默认角色
      expect(screen.getByText("普通用户")).toBeInTheDocument();
    });
  });

  it("应该处理role为null的情况", async () => {
    // Mock role为null的用户数据
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: null,
        title: "新手",
      },
    });

    render(<ProfileCard />);

    await waitFor(() => {
      // 检查是否显示默认角色
      expect(screen.getByText("普通用户")).toBeInTheDocument();
    });
  });

  it("应该优先显示display_name", async () => {
    // Mock 有display_name的用户数据
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: {
          name: "moderator",
          display_name: "版主",
        },
        title: "活跃用户",
      },
    });

    render(<ProfileCard />);

    await waitFor(() => {
      // 检查是否显示display_name而不是name
      expect(screen.getByText("版主")).toBeInTheDocument();
      expect(screen.queryByText("moderator")).not.toBeInTheDocument();
    });
  });

  it("应该在没有display_name时使用映射的角色名", async () => {
    // Mock 只有name的用户数据
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: {
          name: "user",
        },
        title: "新手",
      },
    });

    render(<ProfileCard />);

    await waitFor(() => {
      // 检查是否显示映射后的角色名
      expect(screen.getByText("普通用户")).toBeInTheDocument();
    });
  });

  it("应该处理未知角色名", async () => {
    // Mock 未知角色的用户数据
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: {
          name: "unknown_role",
        },
        title: "新手",
      },
    });

    render(<ProfileCard />);

    await waitFor(() => {
      // 检查是否显示原始角色名
      expect(screen.getByText("unknown_role")).toBeInTheDocument();
    });
  });

  it("应该处理空的role对象", async () => {
    // Mock 空的role对象
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: {},
        title: "新手",
      },
    });

    render(<ProfileCard />);

    await waitFor(() => {
      // 检查是否显示默认角色
      expect(screen.getByText("普通用户")).toBeInTheDocument();
    });
  });
});
