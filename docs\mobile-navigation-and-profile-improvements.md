# 移动端导航栏和个人中心优化

## 概述

本文档记录了对移动端导航栏布局和个人中心页面的优化改进，主要解决了以下问题：

1. 移动设备导航栏登录后个人相关菜单的布局优化
2. 活动统计总积分显示undefined的问题
3. 用户角色显示undefined的问题
4. 个人中心未登录状态的用户体验优化

## 修改内容

### 1. 移动端导航栏布局优化

**文件**: `src/components/Navigation.tsx`

**问题**: 移动设备登录后，个人相关菜单项（个人资料、编辑资料、积分历史等）被放置在底部的单独区域，与主菜单分离。

**解决方案**: 
- 将登录后的个人菜单项直接整合到主菜单区域
- 保持用户信息显示在个人菜单项之前
- 未登录时的登录/注册按钮仍保持在底部区域

**主要变更**:
```typescript
// 登录后的个人相关菜单项 - 直接放在主菜单区域
{isAuthenticated && (
  <>
    {/* 用户信息显示 */}
    <div className="px-3 py-2 border-t border-border-color mt-2 pt-2">
      <div className="flex items-center space-x-2">
        <UserIcon className="h-5 w-5 text-foreground" />
        <div>
          <p className="text-sm font-medium text-foreground">
            {user?.username}
          </p>
          <p className="text-xs text-secondary-text">
            {user?.email}
          </p>
        </div>
      </div>
    </div>
    
    {/* 个人菜单项 */}
    <Link href="/profile">个人资料</Link>
    <Link href="/profile/edit">编辑资料</Link>
    <Link href="/profile/points">积分历史</Link>
    {/* ... */}
  </>
)}
```

### 2. 活动统计数据undefined处理

**文件**: `src/app/profile/page.tsx`

**问题**: 当后端返回的统计数据包含undefined值时，前端直接显示"undefined"文本。

**解决方案**: 使用空值合并操作符(??)为所有统计数据提供默认值。

**主要变更**:
```typescript
<StatCard
  title="总积分"
  value={stats.total_points ?? 0}
  icon={Award}
  description={`当前等级：${stats.current_title ?? '新手'}`}
  onClick={() => router.push("/profile/points")}
/>

<StatCard
  title="发起求助"
  value={stats.total_help_requests ?? 0}
  // ...
/>

// 采纳率计算也进行了安全处理
description={`采纳率：${
  (stats.total_help_answers ?? 0) > 0
    ? Math.round(
        ((stats.accepted_answers ?? 0) / (stats.total_help_answers ?? 1)) * 100
      )
    : 0
}%`}
```

### 3. 用户角色undefined处理

**文件**: `src/hooks/useAuth.ts` 和 `src/components/profile/ProfileCard.tsx`

**问题**: 当用户角色信息为undefined时，权限检查和角色显示会出现错误。

**解决方案**: 
- 在useAuth hook中增加对role的null检查
- 在ProfileCard中创建专门的角色文本处理函数

**主要变更**:

`src/hooks/useAuth.ts`:
```typescript
const hasPermission = useCallback(
  (requiredRole: string) => {
    if (!authState.user || !authState.user.role) {
      return false;
    }
    // ...
  },
  [authState.user]
);
```

`src/components/profile/ProfileCard.tsx`:
```typescript
// 获取角色显示文本
const getRoleText = (role: any) => {
  if (!role) return "普通用户";
  
  const roleMap: { [key: string]: string } = {
    admin: "管理员",
    moderator: "版主",
    user: "普通用户",
  };
  
  return role.display_name || roleMap[role.name] || role.name || "普通用户";
};

// 使用
<span className="text-sm">{getRoleText(profile.role)}</span>
```

### 4. 个人中心未登录状态优化

**文件**: `src/app/profile/page.tsx`

**问题**: 个人中心页面在未登录时显示错误提示和重试按钮，用户体验不佳。

**解决方案**: 创建专门的未登录状态组件，提供友好的提示和自动跳转功能。

**主要变更**:
```typescript
// 未登录状态组件
const UnauthorizedFallback = () => {
  const router = useRouter();

  useEffect(() => {
    // 延迟3.5秒后跳转到登录界面
    const timer = setTimeout(() => {
      router.push('/login?redirect=' + encodeURIComponent('/profile'));
    }, 3500);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <PageContainer>
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="mb-6">
            <User className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
            <h2 className="text-2xl font-semibold text-foreground mb-2">
              未登录
            </h2>
            <p className="text-muted-foreground mb-4">
              您需要登录后才能查看个人中心
            </p>
            <p className="text-sm text-muted-foreground">
              3.5秒后将自动跳转到登录页面...
            </p>
          </div>
          
          <Button 
            onClick={() => router.push('/login?redirect=' + encodeURIComponent('/profile'))}
            className="w-full"
          >
            立即登录
          </Button>
        </div>
      </div>
    </PageContainer>
  );
};

// 在页面中使用
<AuthGuard requireAuth={true} fallback={<UnauthorizedFallback />}>
  {/* 页面内容 */}
</AuthGuard>
```

## 测试覆盖

为所有修改创建了相应的测试文件：

1. `src/tests/components/Navigation.mobile.test.tsx` - 移动端导航栏布局测试
2. `src/tests/pages/profile/profile.stats.test.tsx` - 统计数据undefined处理测试
3. `src/tests/pages/profile/profile.unauthorized.test.tsx` - 未登录状态处理测试
4. `src/tests/components/profile/ProfileCard.role.test.tsx` - 用户角色显示测试

## 用户体验改进

1. **移动端导航更直观**: 登录后的个人菜单项与主菜单整合，减少了用户的认知负担
2. **数据显示更稳定**: 统计数据不再显示undefined，始终显示有意义的数值
3. **角色信息更可靠**: 用户角色显示有适当的fallback，不会出现undefined
4. **未登录体验更友好**: 提供清晰的提示和自动跳转，移除了无意义的重试按钮

## 兼容性说明

所有修改都保持了向后兼容性，不会影响现有功能的正常使用。对于undefined或null的数据，都提供了合理的默认值。
