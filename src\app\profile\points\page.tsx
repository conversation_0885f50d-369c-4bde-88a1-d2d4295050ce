"use client";

import React from "react";
import { PointsHistory } from "@/components/profile/PointsHistory";
import { PointsRules } from "@/components/profile/PointsRules";
import { PointsLeaderboard } from "@/components/profile/PointsLeaderboard";
import { PageContainer } from "@/components/layout/PageContainer";
import AuthGuard from "@/components/AuthGuard";
import { Award } from "lucide-react";

export default function PointsHistoryPage() {
  return (
    <AuthGuard>
      <PageContainer>
        <div className="py-8">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-display font-bold flex items-center">
              <Award className="h-8 w-8 mr-3 text-blue-600" />
              积分历史
            </h1>
            <p className="text-muted-foreground mt-2">
              查看您的积分获得和消费记录
            </p>
          </div>

          <div className="space-y-8">
            {/* 积分历史记录 */}
            <div>
              <PointsHistory />
            </div>

            {/* 积分获取说明和积分排行榜 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 积分获取说明 */}
              <div>
                <PointsRules />
              </div>

              {/* 积分排行榜 */}
              <div>
                <PointsLeaderboard limit={10} />
              </div>
            </div>
          </div>
        </div>
      </PageContainer>
    </AuthGuard>
  );
}
