/**
 * 移动端导航栏测试
 * 测试登录后个人菜单项的布局调整
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import Navigation from "@/components/Navigation";
import { useAuth } from "@/hooks/useAuth";

// Mock dependencies
vi.mock("@/hooks/useAuth");

vi.mock("@/hooks/useTheme", () => ({
  useTheme: () => ({
    theme: "light",
    setTheme: vi.fn(),
  }),
}));

vi.mock("@/hooks/useMounted", () => ({
  useMounted: () => true,
}));

vi.mock("next/navigation", () => ({
  usePathname: () => "/",
  useRouter: () => ({
    push: vi.fn(),
  }),
}));

describe("Navigation - Mobile Layout", () => {
  const mockUseAuth = vi.mocked(useAuth);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该在登录后将个人菜单项显示在主菜单区域", async () => {
    // Mock 已登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: { name: "user" },
      },
      isLoading: false,
      logout: vi.fn(),
      isAdmin: () => false,
    });

    render(<Navigation />);

    // 点击移动端菜单按钮
    const menuButton = screen.getByRole("button", { name: /打开菜单/i });
    fireEvent.click(menuButton);

    await waitFor(() => {
      // 检查用户信息是否显示
      expect(screen.getByText("testuser")).toBeInTheDocument();
      expect(screen.getByText("<EMAIL>")).toBeInTheDocument();

      // 检查个人菜单项是否在主菜单区域
      expect(screen.getByText("个人资料")).toBeInTheDocument();
      expect(screen.getByText("编辑资料")).toBeInTheDocument();
      expect(screen.getByText("积分历史")).toBeInTheDocument();
      expect(screen.getByText("退出登录")).toBeInTheDocument();
    });
  });

  it("应该在未登录时不显示登录注册按钮", async () => {
    // Mock 未登录状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null,
      isLoading: false,
      logout: vi.fn(),
      isAdmin: () => false,
    });

    render(<Navigation />);

    // 点击移动端菜单按钮
    const menuButton = screen.getByRole("button", { name: /打开菜单/i });
    fireEvent.click(menuButton);

    await waitFor(() => {
      // 检查登录注册按钮不应该存在
      expect(screen.queryByText("登录")).not.toBeInTheDocument();
      expect(screen.queryByText("注册")).not.toBeInTheDocument();

      // 检查不应该显示个人菜单项
      expect(screen.queryByText("个人资料")).not.toBeInTheDocument();
      expect(screen.queryByText("编辑资料")).not.toBeInTheDocument();
    });
  });

  it("应该为管理员显示管理后台链接", async () => {
    // Mock 管理员状态
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: {
        id: 1,
        username: "admin",
        email: "<EMAIL>",
        role: { name: "admin" },
      },
      isLoading: false,
      logout: vi.fn(),
      isAdmin: () => true,
    });

    render(<Navigation />);

    // 点击移动端菜单按钮
    const menuButton = screen.getByRole("button", { name: /打开菜单/i });
    fireEvent.click(menuButton);

    await waitFor(() => {
      // 检查管理后台链接是否显示
      expect(screen.getByText("管理后台")).toBeInTheDocument();
    });
  });
});
